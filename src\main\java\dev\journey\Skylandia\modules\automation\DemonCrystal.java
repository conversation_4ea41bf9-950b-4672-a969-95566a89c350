/*
 * DemonCrystal - Advanced Crystal Combat System for Skylandia
 *
 * A demonic crystal combat module that unleashes hell upon your enemies with intelligent
 * multi-crystal placement, adaptive targeting, and emergency escape mechanisms.
 *
 * Features:
 * - Multi-Crystal Warfare: Simultaneous placement at multiple strategic locations
 * - Demonic Auto-Pearl: Emergency escape when death approaches
 * - Intelligent damage calculation and prediction
 * - Advanced rotation and timing systems
 * - Comprehensive safety mechanisms
 */

package dev.journey.Skylandia.modules.automation;

import net.minecraft.block.Block;
import com.google.common.util.concurrent.AtomicDouble;
import it.unimi.dsi.fastutil.ints.*;
import meteordevelopment.meteorclient.events.entity.EntityAddedEvent;
import meteordevelopment.meteorclient.events.entity.EntityRemovedEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IBox;
import meteordevelopment.meteorclient.mixininterface.IMiningToolItem;
import meteordevelopment.meteorclient.mixininterface.IRaycastContext;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.friends.Friends;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.entity.Target;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.RenderUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockIterator;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.TickRate;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.item.*;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.world.RaycastContext;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.*;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.world.RaycastContext;
import org.joml.Vector3d;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.io.File;
import java.util.stream.Collectors;

public class DemonCrystal extends Module {
// Register notification handlers (should be done once, e.g. in module init)
static {
    dev.journey.Skylandia.utils.NotificationRegistry.registerHandler(new dev.journey.Skylandia.utils.ToastNotificationHandler());
    dev.journey.Skylandia.utils.NotificationRegistry.registerHandler(new dev.journey.Skylandia.utils.ChatNotificationHandler());
}

    // === ORGANIZED SETTING GROUPS ===
    // Core functionality
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("🎯 Target Acquisition");
    private final SettingGroup sgPlacement = settings.createGroup("💎 Crystal Placement");
    private final SettingGroup sgBreaking = settings.createGroup("💥 Crystal Breaking");

    // Advanced combat features
    private final SettingGroup sgMultiCrystal = settings.createGroup("⚡ Multi-Crystal Warfare");
    private final SettingGroup sgTrapping = settings.createGroup("🕳️ Target Trapping");
    private final SettingGroup sgCityMining = settings.createGroup("⛏️ City Mining");

    // Safety and automation
    private final SettingGroup sgSafety = settings.createGroup("🛡️ Safety Systems");
    private final SettingGroup sgAutoPearl = settings.createGroup("🌀 Emergency Escape");
    private final SettingGroup sgSwitching = settings.createGroup("🔄 Item Management");

    // Performance and timing
    private final SettingGroup sgTiming = settings.createGroup("⏱️ Timing & Performance");
    private final SettingGroup sgBlockPlacement = settings.createGroup("🧱 Block Placement");

    // Movement enhancements
    private final SettingGroup sgStep = settings.createGroup("👟 Movement Enhancement");
    private final SettingGroup sgFastUse = settings.createGroup("⚡ Fast Actions");
    private final SettingGroup sgHoleAnchor = settings.createGroup("⚓ Hole Anchoring");
    private final SettingGroup sgHoleSnap = settings.createGroup("🎯 Hole Navigation");
    private final SettingGroup sgStrafe = settings.createGroup("🏃 Advanced Strafe");
    private final SettingGroup sgHoleFill = settings.createGroup("🕳️ Hole Filling");

    // Visual and debug
    private final SettingGroup sgRender = settings.createGroup("🎨 Visual Rendering");

    // Block placement speed control
    private final Setting<Double> blocksPerSecond = sgBlockPlacement.add(new DoubleSetting.Builder()
        .name("blocks-per-second")
        .description("Maximum blocks placed per second for defensive structures. Higher values = faster building but may trigger anti-cheat.")
        .defaultValue(4.0)
        .min(0.5)
        .max(20.0)
        .sliderMax(10.0)
        .build()
    );

    // Block placement state tracking
    private long lastBlockPlaceTime = 0;
    private int blocksPlacedThisSecond = 0;
    private long blockPlaceSecond = 0;
   // === ENUMS ===

   public enum RotationMode {
       None("None"),
       Break("Break Only"),
       Place("Place Only"),
       Both("Both");

       private final String name;
       RotationMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum SwitchMode {
       Disabled("Disabled"),
       Normal("Normal"),
       Silent("Silent"),
       Instant("Instant");

       private final String name;
       SwitchMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum SupportMode {
       Disabled("Disabled"),
       Accurate("Accurate"),
       Fast("Fast"),
       Smart("Smart");

       private final String name;
       SupportMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum MultiCrystalMode {
       Disabled("Disabled"),
       Dual("Dual Crystal"),
       Triple("Triple Crystal"),
       Quad("Quad Crystal"),
       Penta("Penta Crystal"),
       Adaptive("Adaptive"),
       Burst("Burst Mode");

       private final String name;
       MultiCrystalMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum PlacementPattern {
       Surrounding("Surrounding"),
       Linear("Linear"),
       Cross("Cross Pattern"),
       Diamond("Diamond"),
       Optimal("Optimal Damage"),
       Trap("Trap Formation");

       private final String name;
       PlacementPattern(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   // New: Escape method preference enum
   public enum EscapeMethod {
       Chorus("Chorus Fruit Only"),
       Pearl("Ender Pearl Only"),
       Both("Chorus Fruit + Ender Pearl");

       private final String name;
       EscapeMethod(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum AutoPearlMode {
       Disabled("Disabled"),
       Health("Health Based"),
       Totems("Totem Based"),
       Combined("Health + Totems"),
       Smart("Smart Escape");

       private final String name;
       AutoPearlMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum TargetPriority {
       Closest("Closest"),
       LowestHealth("Lowest Health"),
       HighestDamage("Highest Damage"),
       MostArmor("Most Armor"),
       LeastArmor("Least Armor");

       private final String name;
       TargetPriority(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum StepMode {
       NCP("NCP"),
       Vanilla("Vanilla");

       private final String name;
       StepMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum HoleSnapMode {
       Move("Movement"),
       Yaw("Yaw Control");

       private final String name;
       HoleSnapMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum StrafeBoost {
       None("None"),
       Elytra("Elytra Boost"),
       Damage("Damage Boost");

       private final String name;
       StrafeBoost(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum HoleFillMode {
       Always("Always"),
       Target("Target Based");

       private final String name;
       HoleFillMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum FillBlocks {
       All("All Blocks"),
       Webs("Cobwebs Only"),
       Obsidian("Obsidian Only"),
       Indestructible("Indestructible Only");

       private final String name;
       FillBlocks(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum SelfFillMode {
       Burrow("Burrow Mode"),
       Trap("Trap Mode");

       private final String name;
       SelfFillMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

    // === ARMOR MENDING SETTINGS ===
    private final SettingGroup sgMending = settings.createGroup("Armor Mending");

    private final Setting<Double> helmetDurability = sgMending.add(new DoubleSetting.Builder()
        .name("helmet-durability-threshold")
        .description("Minimum helmet durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> chestDurability = sgMending.add(new DoubleSetting.Builder()
        .name("chestplate-durability-threshold")
        .description("Minimum chestplate durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> leggingsDurability = sgMending.add(new DoubleSetting.Builder()
        .name("leggings-durability-threshold")
        .description("Minimum leggings durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> bootsDurability = sgMending.add(new DoubleSetting.Builder()
        .name("boots-durability-threshold")
        .description("Minimum boots durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Boolean> pauseWhileMending = sgMending.add(new BoolSetting.Builder()
        .name("pause-while-mending")
        .description("Pause all non-safety actions while mending armor with XP bottles.")
        .defaultValue(true)
        .build()
    );

    // Maximum durability targets
    private final Setting<Double> helmetMaxDurability = sgMending.add(new DoubleSetting.Builder()
        .name("helmet-max-durability")
        .description("Target maximum helmet durability percentage to reach when mending.")
        .defaultValue(95.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    private final Setting<Double> chestMaxDurability = sgMending.add(new DoubleSetting.Builder()
        .name("chestplate-max-durability")
        .description("Target maximum chestplate durability percentage to reach when mending.")
        .defaultValue(95.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    private final Setting<Double> leggingsMaxDurability = sgMending.add(new DoubleSetting.Builder()
        .name("leggings-max-durability")
        .description("Target maximum leggings durability percentage to reach when mending.")
        .defaultValue(95.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    private final Setting<Double> bootsMaxDurability = sgMending.add(new DoubleSetting.Builder()
        .name("boots-max-durability")
        .description("Target maximum boots durability percentage to reach when mending.")
        .defaultValue(95.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    // Mending behavior settings
    private final Setting<Boolean> enableAutoMending = sgMending.add(new BoolSetting.Builder()
        .name("enable-auto-mending")
        .description("Automatically throw exp bottles to mend armor when durability drops below threshold.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> mendingDelay = sgMending.add(new IntSetting.Builder()
        .name("mending-delay")
        .description("Delay in ticks between throwing exp bottles during mending.")
        .defaultValue(3)
        .min(0)
        .max(20)
        .sliderMax(10)
        .visible(enableAutoMending::get)
        .build()
    );

    private final Setting<Integer> maxExpBottlesPerTick = sgMending.add(new IntSetting.Builder()
        .name("max-exp-bottles-per-tick")
        .description("Maximum exp bottles to throw per tick during mending.")
        .defaultValue(1)
        .min(1)
        .max(5)
        .sliderMax(3)
        .visible(enableAutoMending::get)
        .build()
    );

    private final Setting<Boolean> mendingRotation = sgMending.add(new BoolSetting.Builder()
        .name("mending-rotation")
        .description("Control rotation when throwing exp bottles for optimal mending.")
        .defaultValue(true)
        .visible(enableAutoMending::get)
        .build()
    );

    private final Setting<MendingRotationMode> mendingRotationMode = sgMending.add(new EnumSetting.Builder<MendingRotationMode>()
        .name("mending-rotation-mode")
        .description("How to rotate when throwing exp bottles. LookDown ensures bottles land at your feet for optimal mending.")
        .defaultValue(MendingRotationMode.LookDown)
        .visible(() -> enableAutoMending.get() && mendingRotation.get())
        .build()
    );

    private final Setting<Boolean> mendingServerSideRotation = sgMending.add(new BoolSetting.Builder()
        .name("mending-server-rotation")
        .description("Send server-side rotation packets to ensure exp bottles land at feet regardless of client view.")
        .defaultValue(true)
        .visible(() -> enableAutoMending.get() && mendingRotation.get())
        .build()
    );

    public enum MendingRotationMode {
        LookUp("Look Up"),
        LookDown("Look Down"),
        LookAtFeet("Look At Feet");

        private final String name;
        MendingRotationMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    // === CORE GENERAL SETTINGS ===
    private final Setting<Boolean> demonMode = sgGeneral.add(new BoolSetting.Builder()
        .name("🔥 demon-mode")
        .description("Unleash maximum crystal warfare potential! Enables all aggressive optimizations and advanced features for devastating combat performance.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> hyperAggressiveMode = sgGeneral.add(new BoolSetting.Builder()
        .name("⚡ hyper-aggressive")
        .description("MAXIMUM DESTRUCTION MODE: Overrides safety limits for fastest possible crystal placement and breaking. Use with extreme caution!")
        .defaultValue(false)
        .build()
    );

    private final Setting<Keybind> hyperAggressiveKeybind = sgGeneral.add(new KeybindSetting.Builder()
        .name("🔑 hyper-aggressive-keybind")
        .description("Keybind to quickly toggle hyper-aggressive mode during intense combat situations.")
        .defaultValue(Keybind.none())
        .build()
    );

    private final Setting<Boolean> pauseOnEat = sgGeneral.add(new BoolSetting.Builder()
        .name("🍎 pause-on-eat")
        .description("Temporarily pause crystal operations while consuming food items. Prevents interference with healing actions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnMine = sgGeneral.add(new BoolSetting.Builder()
        .name("⛏️ pause-on-mine")
        .description("Pause crystal combat while mining blocks to avoid conflicts with manual mining operations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<RotationMode> rotationMode = sgGeneral.add(new EnumSetting.Builder<RotationMode>()
        .name("🔄 rotation-mode")
        .description("Server-side rotation behavior: None=no rotations, Break=only when breaking, Place=only when placing, Both=always rotate")
        .defaultValue(RotationMode.Both)
        .build()
    );

    private final Setting<Double> rotationSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("🎯 rotation-speed")
        .description("Maximum rotation degrees per tick. Lower values appear more human-like but may reduce effectiveness (1-350°)")
        .defaultValue(180)
        .range(1, 350)
        .sliderMax(180)
        .build()
    );

    private final Setting<Boolean> autoGap = sgGeneral.add(new BoolSetting.Builder()
        .name("🍯 auto-gap")
        .description("Automatically consume golden apples when health drops below threshold. Essential for survival in crystal combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> gapHealthThreshold = sgGeneral.add(new IntSetting.Builder()
        .name("❤️ gap-health-threshold")
        .description("Health level (including absorption hearts) to trigger automatic golden apple consumption. Higher = safer.")
        .defaultValue(16)
        .min(1)
        .sliderMax(40)
        .build()
    );

    // === INTELLIGENT TARGET ACQUISITION ===
    private final Setting<Double> targetRange = sgTargeting.add(new DoubleSetting.Builder()
        .name("🎯 target-range")
        .description("Maximum distance to scan for enemies. Larger range = more targets but higher resource usage. Recommended: 8-15 blocks.")
        .defaultValue(12)
        .min(1)
        .sliderMax(20)
        .build()
    );

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
        .name("🏹 target-priority")
        .description("Target selection strategy: Closest=nearest enemy, LowestHealth=weakest target, HighestDamage=maximum crystal damage potential")
        .defaultValue(TargetPriority.LowestHealth)
        .build()
    );

    private final Setting<Boolean> predictMovement = sgTargeting.add(new BoolSetting.Builder()
        .name("🔮 predict-movement")
        .description("Advanced movement prediction: Place crystals where enemies will be, not where they currently are. Greatly improves hit rate.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionTicks = sgTargeting.add(new DoubleSetting.Builder()
        .name("⏰ prediction-ticks")
        .description("Prediction lookahead in ticks. Higher values predict further ahead but may be less accurate for erratic movement.")
        .defaultValue(3)
        .min(1)
        .max(60)
        .sliderMax(10)
        .visible(predictMovement::get)
        .build()
    );

    private final Setting<Boolean> ignoreNaked = sgTargeting.add(new BoolSetting.Builder()
        .name("👤 ignore-naked")
        .description("Skip players without armor. Saves crystals for armored threats but may miss easy kills.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Set<EntityType<?>>> targetEntities = sgTargeting.add(new EntityTypeListSetting.Builder()
        .name("👹 target-entities")
        .description("Entity types to attack with crystals. Players are primary targets, but bosses like Warden/Wither can also be targeted.")
        .onlyAttackable()
        .defaultValue(EntityType.PLAYER, EntityType.WARDEN, EntityType.WITHER)
        .build()
    );

    private final Setting<Boolean> smartTargeting = sgTargeting.add(new BoolSetting.Builder()
        .name("🧠 smart-targeting")
        .description("AI-enhanced targeting considering damage ratios, armor values, positioning, and threat assessment for optimal target selection.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> targetSwitching = sgTargeting.add(new BoolSetting.Builder()
        .name("🔄 target-switching")
        .description("Dynamically switch between targets based on damage potential and tactical advantage. Maximizes overall combat effectiveness.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> switchThreshold = sgTargeting.add(new DoubleSetting.Builder()
        .name("⚖️ switch-threshold")
        .description("Minimum damage advantage required to switch targets. Higher values = more stable targeting, lower = more aggressive switching.")
        .defaultValue(4.0)
        .min(0)
        .sliderMax(20)
        .visible(targetSwitching::get)
        .build()
    );

    private final Setting<Boolean> predictiveTargeting = sgTargeting.add(new BoolSetting.Builder()
        .name("🎱 predictive-targeting")
        .description("Advanced AI prediction system that anticipates enemy movement patterns and positions crystals for maximum hit probability.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionAccuracy = sgTargeting.add(new DoubleSetting.Builder()
        .name("🎯 prediction-accuracy")
        .description("Prediction algorithm precision. Higher accuracy = better predictions but increased CPU usage. 0.8 recommended for best balance.")
        .defaultValue(0.8)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(predictiveTargeting::get)
        .build()
    );

    // === ADVANCED CRYSTAL PLACEMENT ===
    private final Setting<Boolean> enablePlacement = sgPlacement.add(new BoolSetting.Builder()
        .name("💎 enable-placement")
        .description("Master switch for crystal placement. Disable only for pure defensive/breaking mode.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> blocksPerAction = sgPlacement.add(new IntSetting.Builder()
        .name("🧱 blocks-per-action")
        .description("Support blocks placed per action cycle. Higher values = faster building but may trigger anti-cheat detection.")
        .defaultValue(1)
        .min(1)
        .max(4)
        .sliderMax(4)
        .build()
    );

    private final Setting<Double> placeRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("📏 place-range")
        .description("Maximum crystal placement distance. Larger range = more placement options but higher detection risk. Optimal: 4-6 blocks.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> wallsRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("🧱 walls-range")
        .description("Placement range when line-of-sight is blocked. Should be lower than normal range for safety.")
        .defaultValue(3.5)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamagePlace = sgPlacement.add(new DoubleSetting.Builder()
        .name("💥 min-damage-place")
        .description("Minimum enemy damage required to justify crystal placement. Higher values = more selective placement, saves crystals.")
        .defaultValue(6.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> placement112 = sgPlacement.add(new BoolSetting.Builder()
        .name("🕰️ legacy-placement")
        .description("Use 1.12 placement rules for compatibility with older servers. May bypass some modern anti-cheat systems.")
        .defaultValue(false)
        .build()
    );

    private final Setting<SupportMode> supportMode = sgPlacement.add(new EnumSetting.Builder<SupportMode>()
        .name("🏗️ support-mode")
        .description("Auto-place support blocks for crystal placement: Disabled=none, Accurate=precise, Fast=quick, Smart=adaptive")
        .defaultValue(SupportMode.Smart)
        .build()
    );

    private final Setting<Boolean> facePlaceMode = sgPlacement.add(new BoolSetting.Builder()
        .name("😈 face-place")
        .description("Aggressive close-range placement targeting enemy feet/head area. Extremely effective against low-health targets.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Keybind> facePlaceKeybind = sgPlacement.add(new KeybindSetting.Builder()
        .name("🔑 face-place-keybind")
        .description("Keybind to quickly toggle face-place mode for finishing low-health enemies.")
        .defaultValue(Keybind.none())
        .build()
    );

    private final Setting<List<Block>> validCrystalBaseBlocks = sgPlacement.add(new BlockListSetting.Builder()
        .name("🗿 valid-base-blocks")
        .description("Blocks that can support crystal placement. Obsidian and Bedrock are standard, others may work on specific servers.")
        .defaultValue(List.of(Blocks.OBSIDIAN, Blocks.BEDROCK))
        .build()
    );

    private final Setting<Double> facePlaceHealth = sgPlacement.add(new DoubleSetting.Builder()
        .name("❤️ face-place-health")
        .description("Enemy health threshold to activate face-place mode. Lower values = more aggressive face-placing.")
        .defaultValue(8.0)
        .min(1)
        .sliderMax(36)
        .visible(facePlaceMode::get)
        .build()
    );

    // === FOOT TARGETING SYSTEM ===
    private final Setting<Boolean> footTargeting = sgPlacement.add(new BoolSetting.Builder()
        .name("👣 foot-targeting")
        .description("Advanced foot targeting: Places crystals near enemy feet and mines obstructing obsidian blocks for maximum damage.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> footTargetRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("📐 foot-target-range")
        .description("Maximum range for foot targeting placement. Closer range = more accurate but riskier positioning.")
        .defaultValue(4.0)
        .min(1)
        .max(6)
        .sliderMax(6)
        .visible(footTargeting::get)
        .build()
    );

    private final Setting<Boolean> mineObsidian = sgPlacement.add(new BoolSetting.Builder()
        .name("⛏️ mine-obsidian")
        .description("Automatically mine obsidian blocks near enemy feet to create crystal placement opportunities.")
        .defaultValue(true)
        .visible(footTargeting::get)
        .build()
    );

    private final Setting<Integer> maxObsidianMine = sgPlacement.add(new IntSetting.Builder()
        .name("🔢 max-obsidian-mine")
        .description("Maximum obsidian blocks to mine per target. Higher values = more aggressive mining but increased exposure time.")
        .defaultValue(2)
        .min(1)
        .max(5)
        .sliderMax(5)
        .visible(() -> footTargeting.get() && mineObsidian.get())
        .build()
    );

    private final Setting<Double> obsidianMineRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("📏 obsidian-mine-range")
        .description("Maximum range to mine obsidian blocks around enemy feet. Should match or be slightly less than foot target range.")
        .defaultValue(3.5)
        .min(1)
        .max(6)
        .sliderMax(6)
        .visible(() -> footTargeting.get() && mineObsidian.get())
        .build()
    );

    // === MULTI-CRYSTAL WARFARE ===
    private final Setting<MultiCrystalMode> multiCrystalMode = sgMultiCrystal.add(new EnumSetting.Builder<MultiCrystalMode>()
        .name("multi-crystal-mode")
        .description("Deploy multiple crystals simultaneously for overwhelming demonic assault.")
        .defaultValue(MultiCrystalMode.Dual)
        .build()
    );

    private final Setting<PlacementPattern> placementPattern = sgMultiCrystal.add(new EnumSetting.Builder<PlacementPattern>()
        .name("placement-pattern")
        .description("Pattern for multi-crystal placement to maximize damage and control.")
        .defaultValue(PlacementPattern.Optimal)
        .build()
    );

    private final Setting<Integer> maxCrystals = sgMultiCrystal.add(new IntSetting.Builder()
        .name("max-crystals")
        .description("Maximum number of crystals to place simultaneously (1-5).")
        .defaultValue(3)
        .min(1)
        .max(5)
        .sliderMax(5)
        .build()
    );

    private final Setting<Double> multiCrystalRange = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-range")
        .description("Maximum distance between multiple crystal placements.")
        .defaultValue(3.0)
        .min(1)
        .sliderMax(8)
        .build()
    );

    private final Setting<Integer> multiCrystalDelay = sgMultiCrystal.add(new IntSetting.Builder()
        .name("multi-delay")
        .description("Delay in ticks between placing multiple crystals.")
        .defaultValue(1)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> multiCrystalSync = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("synchronized-detonation")
        .description("Synchronize detonation of multiple crystals for maximum devastation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> multiCrystalMinDamage = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-min-damage")
        .description("Minimum combined damage from all crystals to justify multi-placement.")
        .defaultValue(12.0)
        .min(0)
        .sliderMax(50)
        .build()
    );

    private final Setting<Boolean> intelligentSpacing = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("intelligent-spacing")
        .description("Automatically calculate optimal spacing between crystals for maximum damage.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> avoidOverlap = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("avoid-overlap")
        .description("Prevent crystal damage overlap to maximize total damage output.")
        .defaultValue(true)
        .build()
    );

    // === TARGET TRAPPING ===
    private final Setting<Boolean> enableTrapping = sgTrapping.add(new BoolSetting.Builder()
        .name("enable-trapping")
        .description("Place blocks near and above targets to trap them for maximum crystal damage.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> trapAbove = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-above")
        .description("Place blocks above the target to prevent vertical escape.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> trapSides = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-sides")
        .description("Place blocks around the target to prevent horizontal escape.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Double> trapRange = sgTrapping.add(new DoubleSetting.Builder()
        .name("trap-range")
        .description("Maximum range to place trap blocks from the target.")
        .defaultValue(4.0)
        .min(1)
        .sliderMax(6)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Integer> maxTrapBlocks = sgTrapping.add(new IntSetting.Builder()
        .name("max-trap-blocks")
        .description("Maximum number of trap blocks to place per target.")
        .defaultValue(6)
        .min(1)
        .sliderMax(12)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> smartTrapping = sgTrapping.add(new BoolSetting.Builder()
        .name("smart-trapping")
        .description("Intelligently place trap blocks based on target movement and escape routes.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> trapOnlyLowHealth = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-only-low-health")
        .description("Only trap targets when they have low health for finishing moves.")
        .defaultValue(false)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Double> trapHealthThreshold = sgTrapping.add(new DoubleSetting.Builder()
        .name("trap-health-threshold")
        .description("Health threshold below which to start trapping targets.")
        .defaultValue(10.0)
        .min(1)
        .sliderMax(20)
        .visible(() -> enableTrapping.get() && trapOnlyLowHealth.get())
        .build()
    );

   // === AUTO-PEARL ESCAPE ===
   // New: Escape method preference setting
   private final Setting<EscapeMethod> escapeMethod = sgAutoPearl.add(new EnumSetting.Builder<EscapeMethod>()
       .name("escape-method")
       .description("Preferred escape method: chorus fruit, ender pearl, or both. If both, chorus fruit is prioritized if available.")
       .defaultValue(EscapeMethod.Both)
       .build()
   );

   private final Setting<AutoPearlMode> autoPearlMode = sgAutoPearl.add(new EnumSetting.Builder<AutoPearlMode>()
       .name("auto-pearl-mode")
       .description("Automatically escape using configured method when death Comes knocking.")
       .defaultValue(AutoPearlMode.Combined)
       .build()
   );

   private final Setting<Double> pearlHealthThreshold = sgAutoPearl.add(new DoubleSetting.Builder()
       .name("health-threshold")
       .description("Health level (including absorption) to trigger emergency escape. set higher for more aggressive escapes.")
       .defaultValue(6.0)
       .min(1)
       .sliderMax(20)
       .build()
   );

   private final Setting<Integer> pearlTotemThreshold = sgAutoPearl.add(new IntSetting.Builder()
       .name("totem-threshold")
       .description("Number of totems remaining to trigger emergency escape. are you affraid of death?")
       .defaultValue(1)
       .min(0)
       .sliderMax(5)
       .build()
   );

   private final Setting<Double> pearlDistance = sgAutoPearl.add(new DoubleSetting.Builder()
       .name("pearl-distance")
       .description("Minimum distance to throw pearls for effective escape. you must be looking where you want throw them. no hand holding.")
       .defaultValue(15.0)
       .min(5)
       .sliderMax(30)
       .build()
   );

   private final Setting<Boolean> pearlOnlyWhenTargeted = sgAutoPearl.add(new BoolSetting.Builder()
       .name("pearl-only-when-targeted")
       .description("Only use emergency escape when actively being targeted by enemies. -auto flinch-")
       .defaultValue(true)
       .build()
   );

   private final Setting<Integer> pearlCooldown = sgAutoPearl.add(new IntSetting.Builder()
       .name("pearl-cooldown")
       .description("Cooldown in ticks between escape attempts to prevent spam. or get around dumb anti-cheat.")
       .defaultValue(40)
       .min(20)
       .sliderMax(100)
       .build()
   );

   private final Setting<Keybind> autoPearlKeybind = sgAutoPearl.add(new KeybindSetting.Builder()
       .name("🔑 auto-pearl-keybind")
       .description("Keybind to manually trigger emergency escape or toggle auto-pearl mode.")
       .defaultValue(Keybind.none())
       .build()
   );

    // === CRYSTAL DESTRUCTION SYSTEM ===
    private final Setting<Boolean> enableBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("💥 enable-breaking")
        .description("Master switch for crystal breaking. Disabling removes all offensive and defensive crystal destruction capabilities.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> breakRange = sgBreaking.add(new DoubleSetting.Builder()
        .name("📏 break-range")
        .description("Maximum crystal breaking distance. Larger range = more targets but higher detection risk. Optimal: 4-6 blocks.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamageBreak = sgBreaking.add(new DoubleSetting.Builder()
        .name("💥 min-damage-break")
        .description("Minimum enemy damage required to break a crystal. Higher values = more selective breaking, prevents wasted actions.")
        .defaultValue(4.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> inhibit = sgBreaking.add(new BoolSetting.Builder()
        .name("🛡️ inhibit-mode")
        .description("Defensive crystal breaking: Instantly destroy enemy crystals to prevent their attacks. Also tracks opponent placement attempts and blocks those spots with obsidian.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Keybind> inhibitKeybind = sgBreaking.add(new KeybindSetting.Builder()
        .name("🔑 inhibit-keybind")
        .description("Keybind to quickly toggle inhibit mode for defensive crystal breaking.")
        .defaultValue(Keybind.none())
        .build()
    );

    private final Setting<Boolean> inhibitPlacement = sgBreaking.add(new BoolSetting.Builder()
        .name("🚫 inhibit-placement")
        .description("Track where opponents try to place crystals and block those spots with obsidian to prevent future placements.")
        .defaultValue(true)
        .visible(inhibit::get)
        .build()
    );

    private final Setting<Double> inhibitRange = sgBreaking.add(new DoubleSetting.Builder()
        .name("📏 inhibit-range")
        .description("Maximum range to place inhibit obsidian blocks from your position.")
        .defaultValue(5.0)
        .min(1)
        .max(6)
        .sliderMax(6)
        .visible(() -> inhibit.get() && inhibitPlacement.get())
        .build()
    );

    private final Setting<Integer> inhibitDelay = sgBreaking.add(new IntSetting.Builder()
        .name("⏱️ inhibit-delay")
        .description("Delay in ticks before placing obsidian on opponent crystal placement spots.")
        .defaultValue(2)
        .min(0)
        .max(20)
        .sliderMax(10)
        .visible(() -> inhibit.get() && inhibitPlacement.get())
        .build()
    );

    private final Setting<Integer> maxInhibitBlocks = sgBreaking.add(new IntSetting.Builder()
        .name("🔢 max-inhibit-blocks")
        .description("Maximum number of inhibit obsidian blocks to place per tick.")
        .defaultValue(2)
        .min(1)
        .max(8)
        .sliderMax(5)
        .visible(() -> inhibit.get() && inhibitPlacement.get())
        .build()
    );

    private final Setting<Integer> breakAttempts = sgBreaking.add(new IntSetting.Builder()
        .name("🔄 break-attempts")
        .description("Maximum breaking attempts per crystal before abandoning. Higher values = more persistent but may waste time.")
        .defaultValue(3)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> burstBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("⚡ burst-breaking")
        .description("Rapid-fire crystal breaking mode. Destroys multiple crystals in quick succession for overwhelming offensive power.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> burstCount = sgBreaking.add(new IntSetting.Builder()
        .name("🔢 burst-count")
        .description("Crystals broken per burst cycle. Higher values = more destruction but increased anti-cheat detection risk.")
        .defaultValue(3)
        .min(1)
        .max(10)
        .sliderMax(10)
        .visible(burstBreaking::get)
        .build()
    );

    private final Setting<Integer> burstDelay = sgBreaking.add(new IntSetting.Builder()
        .name("⏱️ burst-delay")
        .description("Milliseconds between burst attacks. Lower values = faster breaking but higher detection risk. 50ms recommended.")
        .defaultValue(50)
        .min(0)
        .sliderMax(200)
        .visible(burstBreaking::get)
        .build()
    );

    private final Setting<Boolean> packetBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("📡 packet-breaking")
        .description("Low-level packet manipulation for maximum breaking speed and anti-cheat bypass. Highly effective but detectable.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> instantBreak = sgBreaking.add(new BoolSetting.Builder()
        .name("⚡ instant-break")
        .description("Break crystals immediately upon detection. Essential for countering enemy crystal placement and maintaining battlefield control.")
        .defaultValue(true)
        .build()
    );

    // === ITEM SWITCHING ===
    private final Setting<SwitchMode> switchMode = sgSwitching.add(new EnumSetting.Builder<SwitchMode>()
        .name("switch-mode")
        .description("How to switch to crystals and tools automatically.")
        .defaultValue(SwitchMode.Normal)
        .build()
    );

    private final Setting<Boolean> antiWeakness = sgSwitching.add(new BoolSetting.Builder()
        .name("anti-weakness")
        .description("Switch to tools when affected by weakness to break crystals effectively.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> noGapSwitch = sgSwitching.add(new BoolSetting.Builder()
        .name("no-gap-switch")
        .description("Don't switch items while holding gapples - prioritize healing.")
        .defaultValue(true)
        .build()
    );

    // === SAFETY SYSTEMS ===
    private final Setting<Boolean> antiSuicide = sgSafety.add(new BoolSetting.Builder()
        .name("anti-suicide")
        .description("Prevent placing/breaking crystals that would kill you - self-preservation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> maxSelfDamage = sgSafety.add(new DoubleSetting.Builder()
        .name("max-self-damage")
        .description("Maximum damage crystals can deal to yourself before being considered unsafe.")
        .defaultValue(8.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> safetyCheck = sgSafety.add(new BoolSetting.Builder()
        .name("safety-check")
        .description("Perform additional safety checks before crystal operations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> emergencyDisable = sgSafety.add(new BoolSetting.Builder()
        .name("emergency-disable")
        .description("Automatically disable module in dangerous situations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> emergencyHealthThreshold = sgSafety.add(new DoubleSetting.Builder()
        .name("emergency-health")
        .description("Health level to trigger emergency disable.")
        .defaultValue(4.0)
        .min(1)
        .sliderMax(20)
        .visible(emergencyDisable::get)
        .build()
    );

    private final Setting<Boolean> totemSafety = sgSafety.add(new BoolSetting.Builder()
        .name("totem-safety")
        .description("Enhanced safety when holding totems.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> minTotems = sgSafety.add(new IntSetting.Builder()
        .name("min-totems")
        .description("Minimum totems required for aggressive crystal combat.")
        .defaultValue(2)
        .min(0)
        .sliderMax(10)
        .visible(totemSafety::get)
        .build()
    );

    private final Setting<Boolean> armorSafety = sgSafety.add(new BoolSetting.Builder()
        .name("armor-safety")
        .description("Adjust aggression based on armor durability.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> minArmorDurability = sgSafety.add(new DoubleSetting.Builder()
        .name("min-armor-durability")
        .description("Minimum armor durability percentage for safe operation.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .visible(armorSafety::get)
        .build()
    );

    private final Setting<Boolean> holeSafety = sgSafety.add(new BoolSetting.Builder()
        .name("hole-safety")
        .description("Only perform aggressive actions when in a safe hole.")
        .defaultValue(false)
        .build()
    );

    /**
     * Enable/disable surround self-protection, block type, and radius.
     */
    private final Setting<Boolean> surroundCheck = sgSafety.add(new BoolSetting.Builder()
        .name("surround-check")
        .description("Enable surround self-protection logic before crystal operations.")
        .defaultValue(true)
        .visible(holeSafety::get)
        .build()
    );
    /**
     * Block type to use for surround/self-protection.
     */
    private final Setting<List<Item>> surroundBlockTypes = sgSafety.add(new ItemListSetting.Builder()
        .name("surround-block-types")
        .description("Block types to use for surround/self-protection (e.g., obsidian, ender chest, etc).")
        .defaultValue(List.of(Items.OBSIDIAN, Items.CRYING_OBSIDIAN, Items.NETHERITE_BLOCK, Items.ENDER_CHEST, Items.RESPAWN_ANCHOR))
        .build()
    );
    /**
     * Radius (distance) for surround/self-protection blocks.
     */
    private final Setting<Integer> surroundRadius = sgSafety.add(new IntSetting.Builder()
        .name("surround-radius")
        .description("Radius (distance) for surround/self-protection blocks (1 = classic surround, 2 = expanded).")
        .defaultValue(1)
        .min(1)
        .max(3)
        .sliderMax(3)
        .build()
    );

    // === TIMING & DELAYS ===
    private final Setting<Integer> placeDelay = sgTiming.add(new IntSetting.Builder()
        .name("place-delay")
        .description("Delay in ticks between crystal placements for timing control.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> breakDelay = sgTiming.add(new IntSetting.Builder()
        .name("break-delay")
        .description("Delay in ticks between crystal breaks for optimal timing.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> switchDelay = sgTiming.add(new IntSetting.Builder()
        .name("switch-delay")
        .description("Delay in ticks after switching items before performing actions.")
        .defaultValue(0)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> speedMode = sgTiming.add(new BoolSetting.Builder()
        .name("speed-mode")
        .description("Enable maximum speed optimizations for crystal combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> noDelayMode = sgTiming.add(new BoolSetting.Builder()
        .name("no-delay-mode")
        .description("Remove all delays for maximum speed (may be less stable).")
        .defaultValue(false)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Integer> tickOptimization = sgTiming.add(new IntSetting.Builder()
        .name("tick-optimization")
        .description("Number of actions to perform per tick (higher = faster but less stable).")
        .defaultValue(2)
        .min(1)
        .max(10)
        .sliderMax(10)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Boolean> packetOptimization = sgTiming.add(new BoolSetting.Builder()
        .name("packet-optimization")
        .description("Optimize packet timing for maximum speed and server compatibility.")
        .defaultValue(true)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Integer> packetDelay = sgTiming.add(new IntSetting.Builder()
        .name("packet-delay")
        .description("Minimum delay between packets in milliseconds.")
        .defaultValue(25)
        .min(0)
        .sliderMax(100)
        .visible(() -> speedMode.get() && packetOptimization.get())
        .build()
    );

    // === VISUAL RENDERING ===
    private final Setting<Boolean> renderPlacement = sgRender.add(new BoolSetting.Builder()
        .name("render-placement")
        .description("Render placement positions for crystal targeting visualization.")
        .defaultValue(true)
        .build()
    );

    // === Enhanced Rendering Controls ===
    private final Setting<Boolean> crystalVisible = sgRender.add(new BoolSetting.Builder()
        .name("crystal-visible")
        .description("Show End Crystal entities.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Boolean> explosionVisible = sgRender.add(new BoolSetting.Builder()
        .name("explosion-visible")
        .description("Show explosion effects.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Double> crystalSize = sgRender.add(new DoubleSetting.Builder()
        .name("crystal-size")
        .description("Scale factor for End Crystal rendering.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderMax(3.0)
        .build()
    );
    private final Setting<Double> explosionSize = sgRender.add(new DoubleSetting.Builder()
        .name("explosion-size")
        .description("Scale factor for explosion rendering.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderMax(3.0)
        .build()
    );
    private final Setting<SettingColor> explosionColor = sgRender.add(new ColorSetting.Builder()
        .name("explosion-color")
        .description("Color for explosion rendering.")
        .defaultValue(new SettingColor(255, 128, 0, 120))
        .build()
    );
    public enum DynamicEffectMode {
        None("None"),
        Pulse("Pulse"),
        Rainbow("Rainbow"),
        Wave("Wave");
        private final String name;
        DynamicEffectMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }
    private final Setting<DynamicEffectMode> crystalDynamicEffect = sgRender.add(new EnumSetting.Builder<DynamicEffectMode>()
        .name("crystal-dynamic-effect")
        .description("Dynamic visual effect for End Crystals.")
        .defaultValue(DynamicEffectMode.None)
        .build()
    );
    private final Setting<DynamicEffectMode> explosionDynamicEffect = sgRender.add(new EnumSetting.Builder<DynamicEffectMode>()
        .name("explosion-dynamic-effect")
        .description("Dynamic visual effect for explosions.")
        .defaultValue(DynamicEffectMode.None)
        .build()
    );

    private final Setting<SettingColor> placementColor = sgRender.add(new ColorSetting.Builder()
        .name("placement-color")
        .description("Color for crystal placement position rendering.")
        .defaultValue(new SettingColor(255, 0, 0, 100))
        .build()
    );

    private final Setting<SettingColor> breakingColor = sgRender.add(new ColorSetting.Builder()
        .name("breaking-color")
        .description("Color for crystal breaking target rendering.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .build()
    );

    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("Rendering mode for crystal position visualization.")
        .defaultValue(ShapeMode.Both)
        .build()
    );

    private final Setting<Boolean> renderTarget = sgRender.add(new BoolSetting.Builder()
        .name("render-target")
        .description("Render current target with highlighting.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> targetColor = sgRender.add(new ColorSetting.Builder()
        .name("target-color")
        .description("Color for target highlighting.")
        .defaultValue(new SettingColor(255, 100, 100, 150))
        .visible(renderTarget::get)
        .build()
    );

    private final Setting<Boolean> renderPrediction = sgRender.add(new BoolSetting.Builder()
        .name("render-prediction")
        .description("Render predicted target movement path.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> predictionColor = sgRender.add(new ColorSetting.Builder()
        .name("prediction-color")
        .description("Color for movement prediction visualization.")
        .defaultValue(new SettingColor(100, 255, 100, 100))
        .visible(renderPrediction::get)
        .build()
    );

    private final Setting<Boolean> renderDamage = sgRender.add(new BoolSetting.Builder()
        .name("render-damage")
        .description("Render damage numbers for crystal positions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> renderTrapBlocks = sgRender.add(new BoolSetting.Builder()
        .name("render-trap-blocks")
        .description("Render trap block positions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> trapColor = sgRender.add(new ColorSetting.Builder()
        .name("trap-color")
        .description("Color for trap block visualization.")
        .defaultValue(new SettingColor(255, 165, 0, 100))
        .visible(renderTrapBlocks::get)
        .build()
    );

    private final Setting<Boolean> renderInhibit = sgRender.add(new BoolSetting.Builder()
        .name("render-inhibit")
        .description("Render inhibit positions where obsidian will be placed to block opponent crystal placements.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> inhibitQueueColor = sgRender.add(new ColorSetting.Builder()
        .name("inhibit-queue-color")
        .description("Color for queued inhibit positions (where obsidian will be placed).")
        .defaultValue(new SettingColor(255, 165, 0, 120))
        .visible(renderInhibit::get)
        .build()
    );

    private final Setting<SettingColor> inhibitPlacedColor = sgRender.add(new ColorSetting.Builder()
        .name("inhibit-placed-color")
        .description("Color for placed inhibit obsidian blocks.")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .visible(renderInhibit::get)
        .build()
    );

    private final Setting<Boolean> debugMode = sgRender.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Enable debug information display.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> renderDebugInfo = sgRender.add(new BoolSetting.Builder()
        .name("render-debug-info")
        .description("Render debug information on screen.")
        .defaultValue(true)
        .visible(debugMode::get)
        .build()
    );

    // === STEP ENHANCEMENT SETTINGS ===
    private final Setting<Boolean> enableStep = sgStep.add(new BoolSetting.Builder()
        .name("enable-step")
        .description("Enable step enhancement for easier movement during crystal combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<StepMode> stepMode = sgStep.add(new EnumSetting.Builder<StepMode>()
        .name("step-mode")
        .description("Step mode for bypassing different anti-cheats.")
        .defaultValue(StepMode.NCP)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Boolean> stepStrict = sgStep.add(new BoolSetting.Builder()
        .name("step-strict")
        .description("Use strict mode for better anti-cheat compatibility.")
        .defaultValue(false)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Double> stepHeight = sgStep.add(new DoubleSetting.Builder()
        .name("step-height")
        .description("Maximum step height in blocks.")
        .defaultValue(2.0)
        .min(1.0)
        .max(2.5)
        .sliderMax(2.5)
        .visible(() -> enableStep.get() && !stepStrict.get())
        .build()
    );

    private final Setting<Boolean> stepUseTimer = sgStep.add(new BoolSetting.Builder()
        .name("step-timer")
        .description("Use timer manipulation for smoother stepping.")
        .defaultValue(true)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Boolean> stepPauseIfShift = sgStep.add(new BoolSetting.Builder()
        .name("pause-if-shift")
        .description("Pause step when sneaking for precise movement.")
        .defaultValue(false)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Integer> stepDelay = sgStep.add(new IntSetting.Builder()
        .name("step-delay")
        .description("Delay between step actions in milliseconds.")
        .defaultValue(200)
        .min(0)
        .max(1000)
        .sliderMax(1000)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Boolean> stepHoleDisable = sgStep.add(new BoolSetting.Builder()
        .name("hole-disable")
        .description("Disable step when entering a safe hole.")
        .defaultValue(false)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Boolean> fastFall = sgStep.add(new BoolSetting.Builder()
        .name("fast-fall")
        .description("Enable fast fall when over a safe hole.")
        .defaultValue(true)
        .visible(enableStep::get)
        .build()
    );

    private final Setting<Double> fastFallSpeed = sgStep.add(new DoubleSetting.Builder()
        .name("fast-fall-speed")
        .description("Speed multiplier for fast fall.")
        .defaultValue(2.0)
        .min(1.0)
        .max(5.0)
        .sliderMax(5.0)
        .visible(() -> enableStep.get() && fastFall.get())
        .build()
    );

    // === FAST USE SETTINGS ===
    private final Setting<Boolean> enableFastUse = sgFastUse.add(new BoolSetting.Builder()
        .name("enable-fast-use")
        .description("Enable fast use for quicker item usage during combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> fastUseDelay = sgFastUse.add(new IntSetting.Builder()
        .name("fast-use-delay")
        .description("Minimum delay between item uses in ticks.")
        .defaultValue(0)
        .min(0)
        .max(5)
        .sliderMax(5)
        .visible(enableFastUse::get)
        .build()
    );

    private final Setting<Boolean> fastUseBlocks = sgFastUse.add(new BoolSetting.Builder()
        .name("fast-use-blocks")
        .description("Apply fast use to block items.")
        .defaultValue(true)
        .visible(enableFastUse::get)
        .build()
    );

    private final Setting<Boolean> fastUseCrystals = sgFastUse.add(new BoolSetting.Builder()
        .name("fast-use-crystals")
        .description("Apply fast use to end crystals.")
        .defaultValue(true)
        .visible(enableFastUse::get)
        .build()
    );

    private final Setting<Boolean> fastUseXP = sgFastUse.add(new BoolSetting.Builder()
        .name("fast-use-xp")
        .description("Apply fast use to experience bottles.")
        .defaultValue(true)
        .visible(enableFastUse::get)
        .build()
    );

    private final Setting<Boolean> fastUseAll = sgFastUse.add(new BoolSetting.Builder()
        .name("fast-use-all")
        .description("Apply fast use to all items.")
        .defaultValue(false)
        .visible(enableFastUse::get)
        .build()
    );

    private final Setting<Integer> fastUseSpeedLimit = sgFastUse.add(new IntSetting.Builder()
        .name("speed-limit")
        .description("Maximum uses per second to prevent anti-cheat detection.")
        .defaultValue(20)
        .min(1)
        .max(50)
        .sliderMax(50)
        .visible(enableFastUse::get)
        .build()
    );

    // === HOLE ANCHORING SETTINGS ===
    private final Setting<Boolean> enableHoleAnchor = sgHoleAnchor.add(new BoolSetting.Builder()
        .name("⚓ enable-hole-anchor")
        .description("Automatically anchor to holes when looking down. Prevents accidental movement out of safe positions during combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> anchorPitch = sgHoleAnchor.add(new IntSetting.Builder()
        .name("📐 anchor-pitch")
        .description("Minimum pitch angle (looking down) to activate hole anchoring. Higher values = more downward look required.")
        .defaultValue(60)
        .min(0)
        .max(90)
        .sliderMax(90)
        .visible(enableHoleAnchor::get)
        .build()
    );

    private final Setting<Boolean> anchorPull = sgHoleAnchor.add(new BoolSetting.Builder()
        .name("🧲 anchor-pull")
        .description("Pull player to hole center when anchoring. Disabled = just stop movement, Enabled = actively center in hole.")
        .defaultValue(true)
        .visible(enableHoleAnchor::get)
        .build()
    );

    private final Setting<Double> anchorPullStrength = sgHoleAnchor.add(new DoubleSetting.Builder()
        .name("💪 pull-strength")
        .description("Strength of the pull towards hole center. Higher values = faster centering but may look suspicious.")
        .defaultValue(0.2)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> enableHoleAnchor.get() && anchorPull.get())
        .build()
    );

    private final Setting<Integer> anchorDepthCheck = sgHoleAnchor.add(new IntSetting.Builder()
        .name("🕳️ depth-check")
        .description("How many blocks down to check for valid holes. Higher values = detects deeper holes but may impact performance.")
        .defaultValue(3)
        .min(1)
        .max(5)
        .sliderMax(5)
        .visible(enableHoleAnchor::get)
        .build()
    );

    // === HOLE NAVIGATION SETTINGS ===
    private final Setting<Boolean> enableHoleSnap = sgHoleSnap.add(new BoolSetting.Builder()
        .name("🎯 enable-hole-snap")
        .description("Automatically navigate to the nearest safe hole. Essential for survival in crystal combat scenarios.")
        .defaultValue(false)
        .build()
    );

    private final Setting<HoleSnapMode> holeSnapMode = sgHoleSnap.add(new EnumSetting.Builder<HoleSnapMode>()
        .name("🎮 snap-mode")
        .description("Navigation method: Movement=direct movement control, Yaw=rotation-based navigation for more natural movement.")
        .defaultValue(HoleSnapMode.Yaw)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Integer> holeSearchRange = sgHoleSnap.add(new IntSetting.Builder()
        .name("🔍 search-range")
        .description("Maximum distance to search for holes. Larger range = more options but higher CPU usage. Recommended: 3-8 blocks.")
        .defaultValue(5)
        .min(1)
        .max(20)
        .sliderMax(20)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Integer> holeSearchFOV = sgHoleSnap.add(new IntSetting.Builder()
        .name("👁️ search-fov")
        .description("Field of view for hole detection. 360° = all directions, lower values = only holes in front of player.")
        .defaultValue(360)
        .min(1)
        .max(360)
        .sliderMax(360)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Boolean> holeUseTimer = sgHoleSnap.add(new BoolSetting.Builder()
        .name("⏱️ use-timer")
        .description("Use timer manipulation for faster hole navigation. Effective but highly detectable by anti-cheat systems.")
        .defaultValue(false)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Double> holeTimerValue = sgHoleSnap.add(new DoubleSetting.Builder()
        .name("⚡ timer-value")
        .description("Timer speed multiplier. Higher values = faster movement but increased detection risk. 1.0 = normal speed.")
        .defaultValue(1.0)
        .min(0.1)
        .max(20.0)
        .sliderMax(5.0)
        .visible(() -> enableHoleSnap.get() && holeUseTimer.get())
        .build()
    );

    private final Setting<Boolean> holeAutoDisableOnDeath = sgHoleSnap.add(new BoolSetting.Builder()
        .name("💀 disable-on-death")
        .description("Automatically disable hole snap when player dies to prevent interference with respawn.")
        .defaultValue(true)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Boolean> holeAutoDisableInHole = sgHoleSnap.add(new BoolSetting.Builder()
        .name("🏠 disable-in-hole")
        .description("Automatically disable when already in a safe hole to prevent unnecessary movement.")
        .defaultValue(true)
        .visible(enableHoleSnap::get)
        .build()
    );

    private final Setting<Boolean> holeAutoDisableNoHoles = sgHoleSnap.add(new BoolSetting.Builder()
        .name("🚫 disable-no-holes")
        .description("Automatically disable when no holes are found in search range to prevent wasted CPU cycles.")
        .defaultValue(false)
        .visible(enableHoleSnap::get)
        .build()
    );

    // === ADVANCED STRAFE SETTINGS ===
    private final Setting<Boolean> enableStrafe = sgStrafe.add(new BoolSetting.Builder()
        .name("🏃 enable-strafe")
        .description("Advanced movement control with speed calculation and boost modes. Essential for high-speed crystal combat movement.")
        .defaultValue(true)
        .build()
    );

    private final Setting<StrafeBoost> strafeBoost = sgStrafe.add(new EnumSetting.Builder<StrafeBoost>()
        .name("🚀 boost-mode")
        .description("Movement boost method: None=standard strafe, Elytra=elytra-based speed, Damage=velocity-based acceleration")
        .defaultValue(StrafeBoost.None)
        .visible(enableStrafe::get)
        .build()
    );

    private final Setting<Double> strafeSpeed = sgStrafe.add(new DoubleSetting.Builder()
        .name("⚡ strafe-speed")
        .description("Base strafe speed multiplier for elytra boost mode. Higher values = faster movement but increased detection risk.")
        .defaultValue(1.3)
        .min(0.1)
        .max(2.0)
        .sliderMax(2.0)
        .visible(() -> enableStrafe.get() && strafeBoost.get() == StrafeBoost.Elytra)
        .build()
    );

    private final Setting<Double> velocityReduction = sgStrafe.add(new DoubleSetting.Builder()
        .name("📉 velocity-reduction")
        .description("Velocity reduction factor for damage boost mode. Higher values = more controlled acceleration from damage.")
        .defaultValue(6.0)
        .min(0.1)
        .max(10.0)
        .sliderMax(10.0)
        .visible(() -> enableStrafe.get() && strafeBoost.get() == StrafeBoost.Damage)
        .build()
    );

    private final Setting<Double> maxVelocitySpeed = sgStrafe.add(new DoubleSetting.Builder()
        .name("🏎️ max-velocity-speed")
        .description("Maximum velocity speed cap for damage boost mode. Prevents excessive speed that triggers anti-cheat.")
        .defaultValue(0.8)
        .min(0.1)
        .max(2.0)
        .sliderMax(2.0)
        .visible(() -> enableStrafe.get() && strafeBoost.get() == StrafeBoost.Damage)
        .build()
    );

    private final Setting<Boolean> strafeSunrise = sgStrafe.add(new BoolSetting.Builder()
        .name("🌅 sunrise-mode")
        .description("Sunrise-compatible elytra boost mode. Enables enhanced elytra mechanics for specific server configurations.")
        .defaultValue(true)
        .visible(() -> enableStrafe.get() && strafeBoost.get() == StrafeBoost.Elytra)
        .build()
    );

    private final Setting<Boolean> strafeDisableFOV = sgStrafe.add(new BoolSetting.Builder()
        .name("👁️ disable-fov-effects")
        .description("Disable FOV effects during strafe for better visual stability. Recommended for consistent aiming during combat.")
        .defaultValue(true)
        .visible(enableStrafe::get)
        .build()
    );

    private final Setting<Boolean> strafeAntiKnockback = sgStrafe.add(new BoolSetting.Builder()
        .name("🛡️ anti-knockback")
        .description("Reduce knockback effects during strafe movement. Helps maintain speed and direction during combat.")
        .defaultValue(true)
        .visible(enableStrafe::get)
        .build()
    );

    private final Setting<Keybind> strafeToggleKeybind = sgStrafe.add(new KeybindSetting.Builder()
        .name("🔑 toggle-keybind")
        .description("Keybind to quickly toggle strafe on/off during combat.")
        .defaultValue(Keybind.none())
        .build()
    );

    private final Setting<Keybind> strafeBoostKeybind = sgStrafe.add(new KeybindSetting.Builder()
        .name("🚀 boost-keybind")
        .description("Keybind to cycle through boost modes (None → Elytra → Damage → None).")
        .defaultValue(Keybind.none())
        .build()
    );

    // === HOLE FILLING SETTINGS ===
    private final Setting<Boolean> enableHoleFill = sgHoleFill.add(new BoolSetting.Builder()
        .name("🕳️ enable-hole-fill")
        .description("Automatically fill holes to deny enemy positioning and create tactical advantages. Essential for area control.")
        .defaultValue(true)
        .build()
    );

    private final Setting<HoleFillMode> holeFillMode = sgHoleFill.add(new EnumSetting.Builder<HoleFillMode>()
        .name("🎯 fill-mode")
        .description("Hole filling strategy: Always=fill all holes in range, Target=only fill holes near enemies for tactical denial")
        .defaultValue(HoleFillMode.Target)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Double> holeFillRange = sgHoleFill.add(new DoubleSetting.Builder()
        .name("📏 fill-range")
        .description("Maximum range to fill holes. Larger range = more area control but higher resource usage. Recommended: 3-6 blocks.")
        .defaultValue(5.0)
        .min(1.0)
        .max(6.0)
        .sliderMax(6.0)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Double> holeFillWallRange = sgHoleFill.add(new DoubleSetting.Builder()
        .name("🧱 wall-range")
        .description("Range to fill holes when line-of-sight is blocked. Should be lower than normal range for safety.")
        .defaultValue(3.5)
        .min(1.0)
        .max(6.0)
        .sliderMax(6.0)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Integer> holeFillBlocksPerTick = sgHoleFill.add(new IntSetting.Builder()
        .name("🧱 blocks-per-tick")
        .description("Blocks placed per tick for hole filling. Higher values = faster filling but increased anti-cheat detection risk.")
        .defaultValue(1)
        .min(1)
        .max(4)
        .sliderMax(4)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Integer> holeFillDelay = sgHoleFill.add(new IntSetting.Builder()
        .name("⏱️ fill-delay")
        .description("Delay in ticks between hole filling actions. Higher values = more legit but slower area control.")
        .defaultValue(0)
        .min(0)
        .max(5)
        .sliderMax(5)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<FillBlocks> holeFillBlocks = sgHoleFill.add(new EnumSetting.Builder<FillBlocks>()
        .name("🗿 fill-blocks")
        .description("Block types for hole filling: All=any blocks, Obsidian=only obsidian, Webs=cobwebs for slowing, Indestructible=durable blocks")
        .defaultValue(FillBlocks.All)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Boolean> holeFillSingle = sgHoleFill.add(new BoolSetting.Builder()
        .name("1️⃣ fill-single")
        .description("Fill single-block holes. Most common hole type, essential for basic area denial.")
        .defaultValue(true)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Boolean> holeFillDouble = sgHoleFill.add(new BoolSetting.Builder()
        .name("2️⃣ fill-double")
        .description("Fill double-block holes. Larger holes that provide more cover, important for comprehensive area control.")
        .defaultValue(false)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Boolean> holeFillQuad = sgHoleFill.add(new BoolSetting.Builder()
        .name("4️⃣ fill-quad")
        .description("Fill quad-block holes. Large holes that provide significant cover, resource-intensive but highly effective.")
        .defaultValue(false)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Boolean> holeFillSelf = sgHoleFill.add(new BoolSetting.Builder()
        .name("👤 self-fill")
        .description("Fill holes under your own position for burrow/trap tactics. Advanced positioning technique.")
        .defaultValue(false)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<SelfFillMode> holeFillSelfMode = sgHoleFill.add(new EnumSetting.Builder<SelfFillMode>()
        .name("🏠 self-fill-mode")
        .description("Self-fill method: Burrow=underground positioning, Trap=overhead block placement for protection")
        .defaultValue(SelfFillMode.Burrow)
        .visible(() -> enableHoleFill.get() && holeFillSelf.get())
        .build()
    );

    private final Setting<Double> holeFillTargetRange = sgHoleFill.add(new DoubleSetting.Builder()
        .name("🎯 target-range")
        .description("Maximum distance from enemies to fill holes in target mode. Closer range = more focused area denial.")
        .defaultValue(2.0)
        .min(1.0)
        .max(5.0)
        .sliderMax(5.0)
        .visible(() -> enableHoleFill.get() && holeFillMode.get() == HoleFillMode.Target)
        .build()
    );

    private final Setting<Boolean> holeFillJumpDisable = sgHoleFill.add(new BoolSetting.Builder()
        .name("🦘 jump-disable")
        .description("Automatically disable hole filling when jumping to prevent interference with movement.")
        .defaultValue(false)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Boolean> holeFillAutoDisable = sgHoleFill.add(new BoolSetting.Builder()
        .name("🔄 auto-disable")
        .description("Automatically disable when all holes in range are filled to conserve resources.")
        .defaultValue(false)
        .visible(enableHoleFill::get)
        .build()
    );

    private final Setting<Keybind> holeFillKeybind = sgHoleFill.add(new KeybindSetting.Builder()
        .name("🔑 hole-fill-keybind")
        .description("Keybind to quickly toggle hole filling on/off during combat.")
        .defaultValue(Keybind.none())
        .build()
    );

    // === FIELDS ===
    private final List<BlockPos> multiCrystalPositions = new ArrayList<>();
    private final List<BlockPos> trapBlockPositions = new ArrayList<>();
    private final List<EndCrystalEntity> targetCrystals = new ArrayList<>();
    private final List<EndCrystalEntity> burstTargets = new ArrayList<>();
    private final Map<BlockPos, Double> positionDamageMap = new HashMap<>();
    private final Map<BlockPos, Integer> placementAttempts = new HashMap<>();
    private final Map<Integer, Integer> crystalBreakAttempts = new HashMap<>();
    private final Map<Integer, Long> crystalLastAttack = new HashMap<>();
    private final List<Entity> potentialTargets = new ArrayList<>();

    // Inhibit placement tracking
    private final Map<BlockPos, Long> opponentPlacementAttempts = new HashMap<>();
    private final Map<BlockPos, Integer> inhibitPlacementQueue = new HashMap<>();
    private final Set<BlockPos> inhibitedPositions = new HashSet<>();

    // City Mining state
    private boolean cityMiningActive = false;
    private PlayerEntity cityMiningTarget = null;
    private BlockPos cityMiningBlock = null;
    private FindItemResult cityMiningPick = null;
    private float cityMiningProgress = 0.0f;
    private boolean cityMiningStarted = false;
    private long cityMiningStartTime = 0;
    private int cityMiningPreviousSlot = -1;

    // === CITY MINING USER SETTINGS ===
    private final Setting<Boolean> cityMiningSupport = sgCityMining.add(new BoolSetting.Builder()
        .name("support-block")
        .description("If there is no block below a city block, place one before mining.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Boolean> cityMiningSwingHand = sgCityMining.add(new BoolSetting.Builder()
        .name("swing-hand")
        .description("Whether to render your hand swinging during city mining.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> cityMiningRenderBlock = sgCityMining.add(new BoolSetting.Builder()
        .name("render-block")
        .description("Whether to render the block being mined during city mining.")
        .defaultValue(true)
        .build()
    );

// === BLOCK PLACEMENT BPS THROTTLE ===
private boolean canPlaceBlockBPS() {
    long now = System.currentTimeMillis();
    long currentSecond = now / 1000;
    if (currentSecond != blockPlaceSecond) {
        blockPlaceSecond = currentSecond;
        blocksPlacedThisSecond = 0;
    }
    if (blocksPlacedThisSecond < blocksPerSecond.get()) {
        blocksPlacedThisSecond++;
        lastBlockPlaceTime = now;
        return true;
    }
    return false;
}
    private final Setting<ShapeMode> cityMiningShapeMode = sgCityMining.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("How the city mining block shapes are rendered.")
        .defaultValue(ShapeMode.Both)
        .visible(cityMiningRenderBlock::get)
        .build()
    );
    private final Setting<SettingColor> cityMiningSideColor = sgCityMining.add(new ColorSetting.Builder()
        .name("side-color")
        .description("The side color of the city mining rendering.")
        .defaultValue(new SettingColor(225, 0, 0, 75))
        .visible(() -> cityMiningRenderBlock.get() && cityMiningShapeMode.get().sides())
        .build()
    );
    private final Setting<SettingColor> cityMiningLineColor = sgCityMining.add(new ColorSetting.Builder()
        .name("line-color")
        .description("The line color of the city mining rendering.")
        .defaultValue(new SettingColor(225, 0, 0, 255))
        .visible(() -> cityMiningRenderBlock.get() && cityMiningShapeMode.get().lines())
        .build()
    );
    // Additional city mining configurability
    private final Setting<Double> cityMiningRange = sgCityMining.add(new DoubleSetting.Builder()
        .name("city-mining-range")
        .description("Maximum range to search for city mining targets.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(12)
        .build()
    );
    private final Setting<List<Item>> cityMiningPickaxeTypes = sgCityMining.add(new ItemListSetting.Builder()
        .name("pickaxe-types")
        .description("Pickaxe types to use for city mining.")
        .defaultValue(List.of(Items.DIAMOND_PICKAXE, Items.NETHERITE_PICKAXE))
        .build()
    );
    private final Setting<Integer> cityMiningDelay = sgCityMining.add(new IntSetting.Builder()
        .name("mining-delay")
        .description("Delay in ticks between mining actions during city mining.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> cityMiningPacketMine = sgCityMining.add(new BoolSetting.Builder()
        .name("packet-mining")
        .description("Use packet-level mining for more reliable block breaking and server validation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> cityMiningInstantSwitch = sgCityMining.add(new BoolSetting.Builder()
        .name("instant-switch")
        .description("Instantly switch to pickaxe when starting city mining for faster response.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> cityMiningValidateBreak = sgCityMining.add(new BoolSetting.Builder()
        .name("validate-break")
        .description("Validate that blocks are fully broken before moving to next target.")
        .defaultValue(true)
        .build()
    );

    // AutoGap state variables
    private boolean gapEating = false;

    // Armor mending state variables
    private boolean isMending = false;
    private int mendingTicks = 0;
    private long lastExpBottleTime = 0;
    private int expBottlesThrown = 0;
    private float originalYaw = 0f;
    private float originalPitch = 0f;
    private boolean rotationStored = false;
    private int gapSlot = -1, gapPrevSlot = -1;
    private Entity currentTarget;
    private Entity lastTarget;
    private Vec3d predictedTargetPos;
    private int pearlCooldownTicks = 0;
    private int placeTicks = 0;
    private int breakTicks = 0;
    private int switchTicks = 0;
    private int burstPlaceCount = 0;
    private int burstBreakCount = 0;
    private boolean emergencyPearlUsed = false;
    private boolean isTrapping = false;
    private long lastPlacementTime = 0;
    private long lastBreakTime = 0;
    private long lastPacketTime = 0;
    private int actionsThisTick = 0;
    private int tickCounter = 0;

    // === DEFENSIVE: Enemy Crystal Placement Tracking ===
    // User-configurable pillar settings
    private final Setting<Integer> defensePillarHeight = sgTrapping.add(new IntSetting.Builder()
        .name("pillar-height")
        .description("Height of defensive pillars placed at enemy crystal positions.")
        .defaultValue(3)
        .min(1)
        .max(8)
        .sliderMax(8)
        .build()
    );
    private final Setting<List<Item>> defensePillarBlockTypes = sgTrapping.add(new ItemListSetting.Builder()
        .name("pillar-block-types")
        .description("Block types to use for defensive pillars (e.g., obsidian, cobblestone, etc).")
        .defaultValue(List.of(Items.OBSIDIAN, Items.COBBLESTONE, Items.STONE, Items.NETHERRACK))
        .build()
    );
    private static final int DEFENSE_PILLAR_QUEUE_SIZE = 8;
    private final Queue<BlockPos> enemyCrystalPositions = new ArrayDeque<>();
    private final Set<BlockPos> recentlyDefended = new HashSet<>();
    private final List<BlockPos> renderPillarPositions = new ArrayList<>();
    // Track pillar progress: base position -> current height
    private final Map<BlockPos, Integer> pillarProgress = new HashMap<>();
    // Debug: count pillar blocks placed this tick
    private int pillarBlocksPlacedThisTick = 0;

    // === STEP STATE VARIABLES ===
    private boolean alreadyInHole = false;
    private boolean stepTimer = false;
    private long lastStepTime = 0;
    private final meteordevelopment.meteorclient.utils.misc.Timer stepDelayTimer = new meteordevelopment.meteorclient.utils.misc.Timer();

    // === FAST USE STATE VARIABLES ===
    private long lastFastUseTime = 0;
    private int fastUseCount = 0;
    private long fastUseSecond = 0;

    // === FOOT TARGETING STATE VARIABLES ===
    private final Map<PlayerEntity, List<BlockPos>> footTargetPositions = new HashMap<>();
    private final Map<BlockPos, Long> obsidianMiningProgress = new HashMap<>();
    private final Set<BlockPos> currentlyMining = new HashSet<>();

    // === HOLE ANCHOR STATE VARIABLES ===
    private boolean isAnchored = false;
    private BlockPos anchoredHole = null;

    // === HOLE SNAP STATE VARIABLES ===
    private BlockPos targetHole = null;
    private float prevClientYaw = 0f;
    private long lastHoleSearch = 0;

    // === STRAFE STATE VARIABLES ===
    private double oldSpeed = 0.0;
    private double contextFriction = 0.91;
    private double originalFovValue = 1.0;
    private boolean needSwap = false;
    private boolean needSprintState = false;
    private boolean strafeDisabled = false;
    private int noSlowTicks = 0;
    private long strafeDisableTime = 0;

    // === HOLE FILL STATE VARIABLES ===
    private int holeFillTickCounter = 0;
    private boolean holeFillSelfNeed = false;
    private final Set<BlockPos> fillingHoles = new HashSet<>();
    private final Map<BlockPos, Long> holeRenderTime = new HashMap<>();
    private long lastHoleFillTime = 0;

    public DemonCrystal() {
        super(Skylandia.Automation, "demon-crystal",
            "§4DemonCrystal§r - Advanced crystal automation with multi-crystal placement, intelligent targeting, " +
            "emergency escape (pearl/chorus), advanced safety (surround, self-protection), armor mending, " +
            "city mining (packet mining), and fully configurable automation and protection.");

    }



    @Override
    public void onActivate() {
        info("§4DemonCrystal§r activated! Preparing for demonic crystal warfare...");


        // Clear all collections
        multiCrystalPositions.clear();
        trapBlockPositions.clear();
        targetCrystals.clear();
        burstTargets.clear();
        positionDamageMap.clear();
        placementAttempts.clear();
        crystalBreakAttempts.clear();
        crystalLastAttack.clear();
        potentialTargets.clear();

        // Clear inhibit tracking
        opponentPlacementAttempts.clear();
        inhibitPlacementQueue.clear();
        inhibitedPositions.clear();

        // Reset state variables
        currentTarget = null;
        lastTarget = null;
        predictedTargetPos = null;
        pearlCooldownTicks = 0;
        placeTicks = 0;
        breakTicks = 0;
        switchTicks = 0;
        burstPlaceCount = 0;
        burstBreakCount = 0;
        emergencyPearlUsed = false;
        isTrapping = false;

        // Reset city mining state
        resetCityMiningState();

        // Reset armor mending state
        isMending = false;
        mendingTicks = 0;
        lastExpBottleTime = 0;
        expBottlesThrown = 0;
        originalYaw = 0f;
        originalPitch = 0f;
        rotationStored = false;

        lastPlacementTime = 0;
        lastBreakTime = 0;
        lastPacketTime = 0;
        actionsThisTick = 0;
        tickCounter = 0;

        // Reset city mining state
        cityMiningActive = false;
        cityMiningTarget = null;
        cityMiningBlock = null;
        cityMiningPick = null;
        cityMiningProgress = 0.0f;

        // Initialize step functionality - Thunder Hack style
        if (enableStep.get() && mc.player != null) {
            alreadyInHole = isInHole(mc.player.getBlockPos());
            stepDelayTimer.reset();
        }

        // Reset step and fast use state
        alreadyInHole = false;
        stepTimer = false;
        lastStepTime = 0;
        lastFastUseTime = 0;
        fastUseCount = 0;
        fastUseSecond = 0;

        // Initialize hole functionality
        if (enableHoleSnap.get()) {
            targetHole = findNearestHole();
            lastHoleSearch = System.currentTimeMillis();
        }

        // Reset hole anchor state
        isAnchored = false;
        anchoredHole = null;
        prevClientYaw = 0f;

        // Initialize strafe system
        if (enableStrafe.get()) {
            oldSpeed = 0.0;
            if (strafeDisableFOV.get() && mc.options != null) {
                originalFovValue = mc.options.getFovEffectScale().getValue();
                mc.options.getFovEffectScale().setValue(0.0);
            }
        }

        if (demonMode.get()) {
            info("§4DEMON MODE ACTIVATED§r - Unleashing maximum crystal fury!");
        }
    }

    @Override
    public void onDeactivate() {
        info("§4DemonCrystal§r deactivated. Crystal warfare ended. did you enjoy the carnage?");

        // Reset step height to default - Thunder Hack style
        if (mc.player != null) {
            setStepHeight(0.6f);
            stepTimer = false;
        }

        // Reset hole functionality
        isAnchored = false;
        anchoredHole = null;
        targetHole = null;
        prevClientYaw = 0f;

        // Reset strafe system
        if (mc.options != null && strafeDisableFOV.get()) {
            mc.options.getFovEffectScale().setValue(originalFovValue);
        }
        oldSpeed = 0.0;
        needSwap = false;
        needSprintState = false;

        // Clear all collections
        multiCrystalPositions.clear();
        trapBlockPositions.clear();
        targetCrystals.clear();
        burstTargets.clear();
        positionDamageMap.clear();
        placementAttempts.clear();
        crystalBreakAttempts.clear();
        crystalLastAttack.clear();
        potentialTargets.clear();

        // Clear inhibit tracking
        opponentPlacementAttempts.clear();
        inhibitPlacementQueue.clear();
        inhibitedPositions.clear();

        // Reset state
        currentTarget = null;
        lastTarget = null;
        predictedTargetPos = null;
        isTrapping = false;

        // Reset city mining state
        resetCityMiningState();

        // Reset armor mending state
        isMending = false;
        mendingTicks = 0;
        lastExpBottleTime = 0;
        expBottlesThrown = 0;
        originalYaw = 0f;
        originalPitch = 0f;
        rotationStored = false;
    }

    // === EVENT HANDLERS ===
    /**
     * Tick handler refactored for instant batch reactions and always-on packet breaking.
     * - Batching/multi-crystal logic is retained.
     * - All batch actions (placement/breaking) are performed instantly per tick.
     * - Packet-level breaking is always enforced.
     * - No unnecessary delays between batch actions.
     * - Documentation and comments updated for clarity.
     */
    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // === STEP FUNCTIONALITY ===
        updateStepLogic();

        // === FAST USE FUNCTIONALITY ===
        updateFastUse();

        // === FOOT TARGETING SYSTEM ===
        if (footTargeting.get() && enablePlacement.get()) {
            updateFootTargeting();
        }

        // === HOLE ANCHOR SYSTEM ===
        if (enableHoleAnchor.get()) {
            updateHoleAnchor();
        }

        // === HOLE SNAP SYSTEM ===
        if (enableHoleSnap.get()) {
            updateHoleSnap();
        }

        // === STRAFE SYSTEM ===
        if (enableStrafe.get()) {
            updateStrafe();
        }

        // === HOLE FILL SYSTEM ===
        if (enableHoleFill.get()) {
            updateHoleFill();
        }

        // === KEYBIND HANDLERS ===
        handleKeybinds();

        // --- Self-Protection: Place blocks around player before crystal actions ---
        placeSelfProtectionBlocks();

        // === AutoGap Integration ===
        if (autoGap.get()) {
            if (gapEating) {
                if (shouldGapEat()) {
                    if (!isGapItem(mc.player.getInventory().getStack(gapSlot))) {
                        int slot = findGapSlot();
                        if (slot == -1) {
                            stopGapEating();
                            return;
                        } else {
                            changeGapSlot(slot);
                        }
                    }
                    gapEat();
                } else {
                    stopGapEating();
                }
                return; // Pause all other actions while eating
            } else if (shouldGapEat()) {
                gapSlot = findGapSlot();
                if (gapSlot != -1) startGapEating();
                return; // Pause all other actions while eating
            }
        }

        // === ARMOR MENDING ===
        if (enableAutoMending.get()) {
            if (isMending) {
                if (shouldContinueMending()) {
                    handleArmorMending();
                    if (pauseWhileMending.get()) {
                        return; // Pause all other actions while mending
                    }
                } else {
                    stopArmorMending();
                }
            } else if (shouldStartMending()) {
                startArmorMending();
                if (pauseWhileMending.get()) {
                    return; // Pause all other actions while mending
                }
            }
        }

        // Reset per-tick counters
        actionsThisTick = 0;
        tickCounter++;

        // Update cooldowns with speed optimizations
        updateCooldowns();

        // Defensive: Build pillars at enemy crystal positions (does not block offense)
        handleDefensivePillars();

        // === City Mining Integration ===
        handleCityMining();

        // Check for emergency escape (chorus fruit or pearl)
        if (shouldUseEscape()) {
            useEscape();
            return;
        }

        // Pause checks
        if (shouldPause()) {
            return;
        }

        // Comprehensive safety checks
        if (!performSafetyChecks()) {
            return;
        }

        // Find target every tick for maximum reactivity
        findTarget();

        if (currentTarget == null) return;

        // Check if we have crystals for placement
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (!crystals.found() && enablePlacement.get()) return;

        // Instantly perform all batch crystal operations (placement and breaking)
        performInstantBatchCrystalOperations();

        // Process inhibit placement queue
        if (inhibit.get() && inhibitPlacement.get()) {
            processInhibitPlacementQueue();
            cleanupOldInhibitData();
        }
    }

    /**
     * Step event handler - Thunder Hack style implementation
     */
    @EventHandler
    private void onStepSync(TickEvent.Post event) {
        if (!enableStep.get() || mc.player == null) return;

        if (stepMode.get() == StepMode.NCP) {
            double stepHeight = mc.player.getY() - mc.player.prevY;

            if (stepHeight <= 0.75 || stepHeight > this.stepHeight.get() ||
                (stepStrict.get() && stepHeight > 1)) return;

            double[] offsets = getStepOffsets(stepHeight);
            if (offsets != null && offsets.length > 1) {
                if (stepUseTimer.get()) {
                    stepTimer = true;
                }

                for (double offset : offsets) {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(
                        mc.player.prevX, mc.player.prevY + offset, mc.player.prevZ, false, false));
                }

                if (stepStrict.get()) {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(
                        mc.player.prevX, mc.player.prevY + stepHeight, mc.player.prevZ, false, false));
                }

                stepDelayTimer.reset();
            }
        }
    }

    // --- Surround-style self-protection logic ---
    private void placeSelfProtectionBlocks() {
        // Only place if on ground and not already surrounded
        if (!mc.player.isOnGround()) return;

        // Only place blocks if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return;

        // Use configured block types for surround/self-protection
        List<Item> blockTypes = surroundBlockTypes.get();
        FindItemResult blockItem = InvUtils.find(blockTypes.toArray(new Item[0]));
        if (!blockItem.found()) return;

        BlockPos playerPos = mc.player.getBlockPos();
        int radius = surroundRadius.get();
        List<BlockPos> surroundOffsets = new ArrayList<>();
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dz = -radius; dz <= radius; dz++) {
                if (Math.abs(dx) + Math.abs(dz) == radius) {
                    surroundOffsets.add(playerPos.add(dx, 0, dz));
                }
            }
        }

        for (BlockPos pos : surroundOffsets) {
            if (!mc.world.getBlockState(pos).isAir()) continue;

            // Check for block below (for placement validity)
            if (mc.world.getBlockState(pos.down()).isAir()) continue;

            // Switch to block
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
            }

            // Rotate if needed
            if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
                Vec3d blockVec = Vec3d.ofCenter(pos);
                Vec3d playerVec = mc.player.getEyePos();
                Vec3d direction = blockVec.subtract(playerVec).normalize();
                float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
                float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
                Rotations.rotate(yaw, pitch);
            }

            // Place the block (BPS throttle)
            if (!canPlaceBlockBPS()) continue;
            BlockHitResult result = new BlockHitResult(
                Vec3d.ofCenter(pos.down()),
                Direction.UP,
                pos.down(),
                false
            );
            mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result);

            // --- Crystal protection: break nearby crystals if block can't be placed ---
            if (mc.world.getBlockState(pos).isAir()) {
                Box box = new Box(
                    pos.getX() - 1, pos.getY() - 1, pos.getZ() - 1,
                    pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1
                );
                for (Entity entity : mc.world.getOtherEntities(null, box)) {
                    if (entity instanceof EndCrystalEntity crystal) {
                        mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(crystal, mc.player.isSneaking()));
                        mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));
                    }
                }
            }
        }
    }

    private void updateCooldowns() {
        if (isHyperAggressive()) {
            pearlCooldownTicks = 0;
            placeTicks = 0;
            breakTicks = 0;
            switchTicks = 0;
        } else if (noDelayMode.get()) {
            // Reset all cooldowns in no-delay mode
            pearlCooldownTicks = Math.max(0, pearlCooldownTicks - 1);
            placeTicks = 0;
            breakTicks = 0;
            switchTicks = 0;
        } else {
            // Normal cooldown updates
            if (pearlCooldownTicks > 0) pearlCooldownTicks--;
            if (placeTicks > 0) placeTicks--;
            if (breakTicks > 0) breakTicks--;
            if (switchTicks > 0) switchTicks--;
        }
    }

    /**
     * Instantly perform all batch crystal operations (placement and breaking) in a single tick.
     * Placement and breaking are both performed for all valid positions/crystals.
     * No batching delays, always uses packet breaking.
     */
    private void performInstantBatchCrystalOperations() {
        // Instantly break all valid crystals in the batch
        if (enableBreaking.get()) {
            performInstantBatchBreaking();
        }
        // Instantly place all valid crystals in the batch
        if (enablePlacement.get()) {
            performInstantBatchPlacement();
        }
        // Trapping logic can remain as is (optional, not batch-critical)
        if (enableTrapping.get()) {
            performSpeedOptimizedTrapping();
        }
    }

    /**
     * Instantly break all valid crystals in the current batch using packet-level breaking.
     * No artificial delays, always uses packets for maximum speed.
     */
    private boolean performInstantBatchBreaking() {
        targetCrystals.clear();
        findTargetCrystals();

        if (targetCrystals.isEmpty()) return false;

        boolean actionPerformed = false;
        for (EndCrystalEntity crystal : targetCrystals) {
            if (shouldBreakCrystal(crystal)) {
                // Always use packet breaking for speed
                performPacketBreak(crystal);
                actionPerformed = true;
            }
        }
        breakTicks = 0;
        return actionPerformed;
    }

    /**
     * Place one crystal per cooldown, in a fast, sequential manner.
     * Uses placeTicks as cooldown, and selects the correct hand.
     */
    private boolean performInstantBatchPlacement() {
        if (placeTicks > 0) return false;

        multiCrystalPositions.clear();
        findMultiCrystalPositions();

        if (multiCrystalPositions.isEmpty()) return false;

        // Place only one crystal per cooldown
        for (BlockPos pos : multiCrystalPositions) {
            if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
            if (placeCrystalWithHand(pos)) {
                placeTicks = placeDelay.get();
                return true;
            }
        }
        return false;
    }

    private boolean performSpeedOptimizedTrapping() {
        if (!enableTrapping.get() || currentTarget == null || isTrapping) return false;

        // Only trap if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return false;

        // Only trap occasionally to not interfere with crystal operations
        if (tickCounter % 10 != 0) return false;

        handleTrapping();
        return true;
    }

    private boolean canPerformPacketAction() {
        if (!packetOptimization.get()) return true;

        long currentTime = System.currentTimeMillis();
        return currentTime - lastPacketTime >= packetDelay.get();
    }

    private void updatePacketTiming() {
        if (packetOptimization.get()) {
            lastPacketTime = System.currentTimeMillis();
        }
    }

    /**
     * Process the inhibit placement queue to place obsidian blocks where opponents tried to place crystals.
     */
    private void processInhibitPlacementQueue() {
        if (inhibitPlacementQueue.isEmpty()) return;

        // Find obsidian in inventory
        FindItemResult obsidian = InvUtils.find(Items.OBSIDIAN);
        if (!obsidian.found()) return;

        // Process queue with delay countdown
        Iterator<Map.Entry<BlockPos, Integer>> iterator = inhibitPlacementQueue.entrySet().iterator();
        int blocksPlaced = 0;

        while (iterator.hasNext() && blocksPlaced < maxInhibitBlocks.get()) {
            Map.Entry<BlockPos, Integer> entry = iterator.next();
            BlockPos pos = entry.getKey();
            int delay = entry.getValue();

            // Countdown delay
            if (delay > 0) {
                entry.setValue(delay - 1);
                continue;
            }

            // Check if position is still valid and in range
            if (!isValidInhibitPosition(pos)) {
                iterator.remove();
                continue;
            }

            // Place obsidian block
            if (placeInhibitObsidian(pos)) {
                inhibitedPositions.add(pos);
                blocksPlaced++;

                if (debugMode.get()) {
                    info("§aPlaced inhibit obsidian at " + pos.toShortString());
                }
            }

            iterator.remove();
        }
    }

    /**
     * Check if a position is valid for inhibit obsidian placement.
     */
    private boolean isValidInhibitPosition(BlockPos pos) {
        if (mc.player == null || mc.world == null) return false;

        // Check range
        if (mc.player.getBlockPos().isWithinDistance(pos, inhibitRange.get()) == false) return false;

        // Check if position is air
        if (!mc.world.getBlockState(pos).isAir()) return false;

        // Check if there's a solid block below (for obsidian placement)
        BlockPos below = pos.down();
        if (!mc.world.getBlockState(below).isSolidBlock(mc.world, below)) return false;

        // Don't place if already inhibited
        if (inhibitedPositions.contains(pos)) return false;

        return true;
    }

    /**
     * Place obsidian block at the specified position for inhibit purposes.
     */
    private boolean placeInhibitObsidian(BlockPos pos) {
        if (mc.player == null || mc.interactionManager == null) return false;

        // Find obsidian in inventory
        FindItemResult obsidian = InvUtils.find(Items.OBSIDIAN);
        if (!obsidian.found()) return false;

        // Switch to obsidian if needed
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(obsidian.slot(), switchMode.get() == SwitchMode.Silent);
            if (switchDelay.get() > 0) {
                return false; // Wait for switch delay
            }
        }

        // Rotate to position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d targetVec = Vec3d.ofCenter(pos);
            Rotations.rotate(Rotations.getYaw(targetVec), Rotations.getPitch(targetVec));
        }

        // Place the block
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result);
        return true;
    }

    /**
     * Clean up old inhibit data to prevent memory leaks.
     */
    private void cleanupOldInhibitData() {
        long currentTime = System.currentTimeMillis();
        long maxAge = 30000; // 30 seconds

        // Remove old placement attempts
        opponentPlacementAttempts.entrySet().removeIf(entry ->
            currentTime - entry.getValue() > maxAge);

        // Remove inhibited positions that no longer have obsidian
        inhibitedPositions.removeIf(pos ->
            mc.world.getBlockState(pos).getBlock() != Blocks.OBSIDIAN);

        // Limit the size of collections to prevent memory issues
        if (opponentPlacementAttempts.size() > 100) {
            opponentPlacementAttempts.clear();
        }
        if (inhibitPlacementQueue.size() > 50) {
            inhibitPlacementQueue.clear();
        }
        if (inhibitedPositions.size() > 200) {
            inhibitedPositions.clear();
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Render crystal placement positions
        if (renderPlacement.get()) {
            for (int i = 0; i < multiCrystalPositions.size(); i++) {
                BlockPos pos = multiCrystalPositions.get(i);

                // Different colors for different priority levels
                Color color = placementColor.get();
                if (multiCrystalPositions.size() > 1) {
                    float alpha = 1.0f - (i * 0.2f); // Fade for lower priority positions
                    color = new Color(color.r, color.g, color.b, (int)(color.a * alpha));
                }

                event.renderer.box(pos, color, color, shapeMode.get(), 0);

                // Render damage numbers if enabled
                if (renderDamage.get() && positionDamageMap.containsKey(pos)) {
                    double damage = positionDamageMap.get(pos);
                    Vec3d textPos = Vec3d.ofCenter(pos).add(0, 1.2, 0);
                    renderDamageText(event, textPos, String.format("%.1f", damage), color);
                }
            }
        }

        // Defensive: Render pillar positions
        if (!renderPillarPositions.isEmpty()) {
            Color pillarColor = new Color(0, 200, 255, 80);
            for (BlockPos pos : renderPillarPositions) {
                for (int y = 0; y < defensePillarHeight.get(); y++) {
                    BlockPos pillarBlock = pos.up(y);
                    event.renderer.box(pillarBlock, pillarColor, pillarColor, shapeMode.get(), 0);
                }
            }
        }

        // Render crystal entities
        if (crystalVisible.get()) {
            for (Entity entity : mc.world.getEntities()) {
                if (!(entity instanceof EndCrystalEntity crystal)) continue;

                // Simple crystal highlight rendering
                event.renderer.box(
                    crystal.getBoundingBox().expand((float)(crystalSize.get() - 1.0)),
                    placementColor.get(),
                    placementColor.get(),
                    ShapeMode.Sides,
                    0
                );
                event.renderer.box(
                    crystal.getBoundingBox().expand((float)(crystalSize.get() - 1.0)),
                    breakingColor.get(),
                    breakingColor.get(),
                    ShapeMode.Lines,
                    0
                );

                // Render break attempts if debug mode and this is a breaking target
                if (debugMode.get() && targetCrystals.contains(crystal)) {
                    int attempts = crystalBreakAttempts.getOrDefault(crystal.getId(), 0);
                    if (attempts > 0) {
                        Vec3d textPos = crystal.getPos().add(0, 1.5, 0);
                        renderDamageText(event, textPos, "A:" + attempts, breakingColor.get());
                    }
                }
            }
        }

        // Render current target
        if (renderTarget.get() && currentTarget != null) {
            event.renderer.box(currentTarget.getBoundingBox().expand(0.1),
                targetColor.get(), targetColor.get(), shapeMode.get(), 0);
        }

        // Render movement prediction
        if (renderPrediction.get() && currentTarget != null && predictedTargetPos != null) {
            Vec3d currentPos = currentTarget.getPos();

            // Draw line from current position to predicted position
            event.renderer.line(currentPos.x, currentPos.y + 1, currentPos.z,
                predictedTargetPos.x, predictedTargetPos.y + 1, predictedTargetPos.z,
                predictionColor.get());

            // Draw predicted position box
            BlockPos predictedBlockPos = BlockPos.ofFloored(predictedTargetPos);
            event.renderer.box(predictedBlockPos, predictionColor.get(), predictionColor.get(),
                ShapeMode.Lines, 0);
        }

        // Render trap blocks
        if (renderTrapBlocks.get() && !trapBlockPositions.isEmpty()) {
            for (BlockPos pos : trapBlockPositions) {
                event.renderer.box(pos, trapColor.get(), trapColor.get(), shapeMode.get(), 0);
            }
        }

        // Render inhibit positions
        if (renderInhibit.get() && inhibit.get() && inhibitPlacement.get()) {
            // Render queued inhibit positions
            for (BlockPos pos : inhibitPlacementQueue.keySet()) {
                event.renderer.box(pos, inhibitQueueColor.get(), inhibitQueueColor.get(), shapeMode.get(), 0);

                // Render countdown text if debug mode
                if (debugMode.get()) {
                    int delay = inhibitPlacementQueue.get(pos);
                    Vec3d textPos = Vec3d.ofCenter(pos).add(0, 0.5, 0);
                    renderDamageText(event, textPos, String.valueOf(delay), inhibitQueueColor.get());
                }
            }

            // Render placed inhibit positions
            for (BlockPos pos : inhibitedPositions) {
                if (mc.world.getBlockState(pos).getBlock() == Blocks.OBSIDIAN) {
                    event.renderer.box(pos, inhibitPlacedColor.get(), inhibitPlacedColor.get(), ShapeMode.Lines, 0);
                }
            }
        }

        // Render city mining block
        if (cityMiningRenderBlock.get() && cityMiningActive && cityMiningBlock != null) {
            // Calculate progress-based color intensity
            float progress = Math.min(1.0f, cityMiningProgress);
            Color sideColor = cityMiningSideColor.get();
            Color lineColor = cityMiningLineColor.get();

            // Modify alpha based on progress (more opaque as mining progresses)
            int progressAlpha = (int)(sideColor.a * (0.3f + 0.7f * progress));
            Color progressSideColor = new Color(sideColor.r, sideColor.g, sideColor.b, progressAlpha);

            event.renderer.box(cityMiningBlock, progressSideColor, lineColor, cityMiningShapeMode.get(), 0);

            // Render progress text if debug mode
            if (debugMode.get()) {
                Vec3d textPos = Vec3d.ofCenter(cityMiningBlock).add(0, 1, 0);
                String progressText = String.format("%.1f%%", progress * 100);
                renderDamageText(event, textPos, progressText, lineColor);
            }
        }

        // Render debug information
        if (debugMode.get()) {
            renderDebugVisualization(event);
        }
    }

    private void renderDamageText(Render3DEvent event, Vec3d pos, String text, Color color) {
        // This would need proper text rendering implementation
        // For now, we'll use a simple box to indicate damage level
        double size = Math.min(0.3, Double.parseDouble(text.replace("A:", "")) * 0.02);
        event.renderer.box(pos.x - size/2, pos.y - size/2, pos.z - size/2,
            pos.x + size/2, pos.y + size/2, pos.z + size/2,
            color, color, ShapeMode.Both, 0);
    }

    private void renderDebugVisualization(Render3DEvent event) {
        if (currentTarget == null) return;

        // Render search area around target
        BlockPos targetPos = currentTarget.getBlockPos();
        Color debugColor = new Color(255, 255, 255, 50);

        // Render placement search radius
        double range = placeRange.get();
        event.renderer.box(
            targetPos.getX() - range, targetPos.getY() - 2, targetPos.getZ() - range,
            targetPos.getX() + range, targetPos.getY() + 3, targetPos.getZ() + range,
            debugColor, debugColor, ShapeMode.Lines, 0
        );

        // Render target velocity vector
        Vec3d velocity = currentTarget.getVelocity();
        if (velocity.lengthSquared() > 0.01) {
            Vec3d start = currentTarget.getPos().add(0, 1, 0);
            Vec3d end = start.add(velocity.multiply(10));
            event.renderer.line(start.x, start.y, start.z, end.x, end.y, end.z,
                new Color(255, 255, 0, 200));
        }
    }

    @EventHandler
    private void onRender2D(Render2DEvent event) {
        if (!debugMode.get() || !renderDebugInfo.get()) return;
        if (mc.player == null || mc.world == null) return;

        TextRenderer textRenderer = TextRenderer.get();
        int y = 10;
        int lineHeight = (int) textRenderer.getHeight() + 2;

        // Module status
        textRenderer.begin(1, false, true);
        textRenderer.render("§4DemonCrystal Debug", 10, y, Color.WHITE);
        y += lineHeight;

        // Target information
        if (currentTarget != null) {
            textRenderer.render("§7Target: §f" + currentTarget.getName().getString(), 10, y, Color.WHITE);
            y += lineHeight;

            if (currentTarget instanceof LivingEntity living) {
                textRenderer.render("§7Health: §f" + String.format("%.1f", living.getHealth()), 10, y, Color.WHITE);
                y += lineHeight;
            }

            double distance = mc.player.distanceTo(currentTarget);
            textRenderer.render("§7Distance: §f" + String.format("%.1f", distance), 10, y, Color.WHITE);
            y += lineHeight;

            if (predictedTargetPos != null) {
                textRenderer.render("§7Predicted: §f" +
                    String.format("%.1f, %.1f, %.1f", predictedTargetPos.x, predictedTargetPos.y, predictedTargetPos.z),
                    10, y, Color.WHITE);
                y += lineHeight;
            }
        } else {
            textRenderer.render("§7Target: §cNone", 10, y, Color.WHITE);
            y += lineHeight;
        }

        // Crystal information
        textRenderer.render("§7Crystal Positions: §f" + multiCrystalPositions.size(), 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Target Crystals: §f" + targetCrystals.size(), 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Trap Blocks: §f" + trapBlockPositions.size(), 10, y, Color.WHITE);
        y += lineHeight;

        // Performance information
        textRenderer.render("§7Actions/Tick: §f" + actionsThisTick, 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Burst Place: §f" + burstPlaceCount, 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Burst Break: §f" + burstBreakCount, 10, y, Color.WHITE);
        y += lineHeight;

        // Safety information
        float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        textRenderer.render("§7Health: §f" + String.format("%.1f", health), 10, y, Color.WHITE);
        y += lineHeight;

        int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
        textRenderer.render("§7Totems: §f" + totems, 10, y, Color.WHITE);
        y += lineHeight;

        // Timing information
        if (packetOptimization.get()) {
            long timeSinceLastPacket = System.currentTimeMillis() - lastPacketTime;
            textRenderer.render("§7Packet Delay: §f" + timeSinceLastPacket + "ms", 10, y, Color.WHITE);
            y += lineHeight;
        }

        // Mode information
        String mode = demonMode.get() ? "§4DEMON" : "§7Normal";
        textRenderer.render("§7Mode: " + mode, 10, y, Color.WHITE);
        y += lineHeight;

        if (speedMode.get()) {
            textRenderer.render("§7Speed: §aEnabled §7(" + tickOptimization.get() + "/tick)", 10, y, Color.WHITE);
            y += lineHeight;
        }

        // Inhibit information
        if (inhibit.get() && inhibitPlacement.get()) {
            String inhibitStatus = "§cEnabled";
            textRenderer.render("§7Inhibit: " + inhibitStatus, 10, y, Color.WHITE);
            y += lineHeight;

            textRenderer.render("§7Queued: §f" + inhibitPlacementQueue.size(), 10, y, Color.WHITE);
            y += lineHeight;

            textRenderer.render("§7Placed: §f" + inhibitedPositions.size(), 10, y, Color.WHITE);
            y += lineHeight;

            textRenderer.render("§7Tracked: §f" + opponentPlacementAttempts.size(), 10, y, Color.WHITE);
            y += lineHeight;
        }

        // City mining information
        if (cityMiningActive) {
            textRenderer.render("§7City Mining: §aActive", 10, y, Color.WHITE);
            y += lineHeight;

            if (cityMiningTarget != null) {
                textRenderer.render("§7Target: §f" + cityMiningTarget.getName().getString(), 10, y, Color.WHITE);
                y += lineHeight;
            }

            if (cityMiningBlock != null) {
                textRenderer.render("§7Block: §f" + cityMiningBlock.toShortString(), 10, y, Color.WHITE);
                y += lineHeight;
            }

            textRenderer.render("§7Progress: §f" + String.format("%.1f%%", cityMiningProgress * 100), 10, y, Color.WHITE);
            y += lineHeight;

            if (cityMiningPick != null) {
                String pickaxeName = mc.player.getInventory().getStack(cityMiningPick.slot()).getItem().getName().getString();
                textRenderer.render("§7Tool: §f" + pickaxeName, 10, y, Color.WHITE);
                y += lineHeight;
            }
        }

        // Armor mending information
        if (enableAutoMending.get() && isMending) {
            textRenderer.render("§7Armor Mending: §aActive", 10, y, Color.WHITE);
            y += lineHeight;

            textRenderer.render("§7Bottles Thrown: §f" + expBottlesThrown, 10, y, Color.WHITE);
            y += lineHeight;

            textRenderer.render("§7Mending Delay: §f" + mendingTicks + " ticks", 10, y, Color.WHITE);
            y += lineHeight;

            // Show rotation information
            if (mendingRotation.get()) {
                textRenderer.render("§7Rotation Mode: §f" + mendingRotationMode.get().toString(), 10, y, Color.WHITE);
                y += lineHeight;

                if (mendingServerSideRotation.get()) {
                    textRenderer.render("§7Server Rotation: §aEnabled", 10, y, Color.WHITE);
                } else {
                    textRenderer.render("§7Server Rotation: §cDisabled", 10, y, Color.WHITE);
                }
                y += lineHeight;

                if (rotationStored) {
                    textRenderer.render("§7Original Yaw: §f" + String.format("%.1f°", originalYaw), 10, y, Color.WHITE);
                    y += lineHeight;
                    textRenderer.render("§7Original Pitch: §f" + String.format("%.1f°", originalPitch), 10, y, Color.WHITE);
                    y += lineHeight;
                }
            }

            // Show armor durability status
            ItemStack helmet = mc.player.getInventory().getArmorStack(3);
            ItemStack chestplate = mc.player.getInventory().getArmorStack(2);
            ItemStack leggings = mc.player.getInventory().getArmorStack(1);
            ItemStack boots = mc.player.getInventory().getArmorStack(0);

            if (!helmet.isEmpty() && helmet.isDamageable()) {
                double durability = getDurabilityPercent(helmet);
                textRenderer.render("§7Helmet: §f" + String.format("%.1f%%", durability), 10, y, Color.WHITE);
                y += lineHeight;
            }

            if (!chestplate.isEmpty() && chestplate.isDamageable()) {
                double durability = getDurabilityPercent(chestplate);
                textRenderer.render("§7Chestplate: §f" + String.format("%.1f%%", durability), 10, y, Color.WHITE);
                y += lineHeight;
            }

            if (!leggings.isEmpty() && leggings.isDamageable()) {
                double durability = getDurabilityPercent(leggings);
                textRenderer.render("§7Leggings: §f" + String.format("%.1f%%", durability), 10, y, Color.WHITE);
                y += lineHeight;
            }

            if (!boots.isEmpty() && boots.isDamageable()) {
                double durability = getDurabilityPercent(boots);
                textRenderer.render("§7Boots: §f" + String.format("%.1f%%", durability), 10, y, Color.WHITE);
                y += lineHeight;
            }
        }

        textRenderer.end();
    }

    // === DEFENSIVE LOGIC ===

    @EventHandler
    private void onEntityAdded(EntityAddedEvent event) {
        // Only track Ender Crystals not placed by the player
        if (!(event.entity instanceof EndCrystalEntity crystal)) return;
        BlockPos pos = event.entity.getBlockPos();

        // Existing defensive pillar logic
        if (!multiCrystalPositions.contains(pos) && !enemyCrystalPositions.contains(pos) && !recentlyDefended.contains(pos)) {
            if (enemyCrystalPositions.size() >= DEFENSE_PILLAR_QUEUE_SIZE) enemyCrystalPositions.poll();
            enemyCrystalPositions.offer(pos);
        }

        // Inhibit placement tracking
        if (inhibit.get() && inhibitPlacement.get() && mc.player != null && mc.world != null) {
            BlockPos crystalPos = crystal.getBlockPos();

            // Check if this crystal was placed near us and we should inhibit the area
            if (mc.player.getBlockPos().isWithinDistance(crystalPos, inhibitRange.get())) {
                // Only track if we didn't place this crystal ourselves
                boolean isOurCrystal = false;

                // Simple check: if we have crystals selected and recently placed, this might be ours
                FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
                if (crystals.found() && crystals.isHotbar() &&
                    (mc.player.getMainHandStack().getItem() == Items.END_CRYSTAL ||
                     mc.player.getOffHandStack().getItem() == Items.END_CRYSTAL)) {
                    isOurCrystal = true;
                }

                if (!isOurCrystal) {
                    // Track this as an opponent placement attempt
                    opponentPlacementAttempts.put(crystalPos, System.currentTimeMillis());

                    // Queue this position for obsidian placement after delay
                    inhibitPlacementQueue.put(crystalPos, inhibitDelay.get());

                    if (debugMode.get()) {
                        info("§cTracked opponent crystal placement at " + crystalPos.toShortString() + " - queuing inhibit");
                    }
                }
            }
        }
    }

    private void handleDefensivePillars() {
        if (enemyCrystalPositions.isEmpty()) return;
        // Only place pillar blocks if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return;

        // Use user-configurable block types for pillars
        List<Item> blockTypes = defensePillarBlockTypes.get();
        FindItemResult blockItem = InvUtils.find(blockTypes.toArray(new Item[0]));
        if (!blockItem.found()) return;

        renderPillarPositions.clear();
        pillarBlocksPlacedThisTick = 0;
        int maxPillarBlocksPerTick = 1; // Only place one pillar block per tick

        Iterator<BlockPos> it = enemyCrystalPositions.iterator();
        while (it.hasNext() && pillarBlocksPlacedThisTick < maxPillarBlocksPerTick) {
            BlockPos base = it.next();
            // Only build if not already recently defended
            if (recentlyDefended.contains(base)) continue;
            boolean built = buildPillarAtOptimized(base, blockItem);
            if (built) {
                recentlyDefended.add(base);
                renderPillarPositions.add(base);
                it.remove();
                pillarProgress.remove(base);
            }
        }
        // Clean up old entries
        if (recentlyDefended.size() > 32) {
            Iterator<BlockPos> clean = recentlyDefended.iterator();
            while (clean.hasNext() && recentlyDefended.size() > 32) clean.next(); clean.remove();
        }
        // Debug log for pillar placement rate
        if (debugMode.get()) {
            info("[DEBUG] Pillar blocks placed this tick: " + pillarBlocksPlacedThisTick);
        }
    }

    // Optimized: Only place one pillar block per tick per base
    private boolean buildPillarAtOptimized(BlockPos base, FindItemResult blockItem) {
        int pillarHeight = defensePillarHeight.get();
        int currentHeight = pillarProgress.getOrDefault(base, 0);

        // Find the next air block in the pillar
        while (currentHeight < pillarHeight) {
            BlockPos pos = base.up(currentHeight);
            if (!mc.world.getBlockState(pos).isAir()) {
                currentHeight++;
                continue;
            }
            // Check for solid block below for first block
            if (currentHeight == 0 && mc.world.getBlockState(pos.down()).isAir()) return false;
            // Switch to block
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
            }
            // Rotate if needed
            if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
                Vec3d blockVec = Vec3d.ofCenter(pos);
                Vec3d playerVec = mc.player.getEyePos();
                Vec3d direction = blockVec.subtract(playerVec).normalize();
                float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
                float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
                Rotations.rotate(yaw, pitch);
            }
            // Place the block (BPS throttle)
            if (!canPlaceBlockBPS()) return false;
            BlockHitResult result = new BlockHitResult(
                Vec3d.ofCenter(pos.down()),
                Direction.UP,
                pos.down(),
                false
            );
            if (mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result).isAccepted()) {
                pillarProgress.put(base, currentHeight + 1);
                pillarBlocksPlacedThisTick++;
                // Only one block per tick
                return pillarProgress.get(base) >= pillarHeight;
            } else {
                // Placement failed, try again next tick
                pillarProgress.put(base, currentHeight);
                return false;
            }
        }
        // Pillar complete
        return true;
    }

    // === CORE METHODS ===
    private boolean isHyperAggressive() {
        return hyperAggressiveMode.get();
    }

    // Handles city mining logic (improved implementation)
    private void handleCityMining() {
        // If not active, try to find a target and block to mine
        if (!cityMiningActive) {
            resetCityMiningState();

            // Find a valid player target within range
            PlayerEntity target = findCityMiningTarget();
            if (target == null) return;

            // Find a city block next to the target's feet
            BlockPos cityBlock = EntityUtils.getCityBlock(target);
            if (cityBlock == null || !isValidCityBlock(cityBlock)) return;

            // Find a pickaxe in hotbar (user-configurable types)
            List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
            FindItemResult pick = InvUtils.find(itemStack -> pickaxeTypes.contains(itemStack.getItem()));
            if (!pick.isHotbar()) return;

            // Activate city mining state
            startCityMining(target, cityBlock, pick);
        }

        // If active, continue mining
        if (cityMiningActive) {
            // Validate mining state
            if (!validateCityMiningState()) {
                stopCityMining();
                return;
            }

            // Continue mining process
            continueCityMining();
        }
    }

    /**
     * Reset all city mining state variables
     */
    private void resetCityMiningState() {
        cityMiningTarget = null;
        cityMiningBlock = null;
        cityMiningPick = null;
        cityMiningProgress = 0.0f;
        cityMiningStarted = false;
        cityMiningStartTime = 0;
        cityMiningPreviousSlot = -1;
    }

    /**
     * Find a valid target for city mining
     */
    private PlayerEntity findCityMiningTarget() {
        PlayerEntity target = null;
        double bestDist = Double.MAX_VALUE;
        double miningRange = cityMiningRange.get();

        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof PlayerEntity player && entity != mc.player &&
                !player.isCreative() && !player.isSpectator() && !Friends.get().isFriend(player)) {
                double dist = mc.player.distanceTo(player);
                if (dist < miningRange && dist < bestDist) {
                    target = player;
                    bestDist = dist;
                }
            }
        }
        return target;
    }

    /**
     * Check if a city block is valid for mining
     */
    private boolean isValidCityBlock(BlockPos cityBlock) {
        if (cityBlock == null) return false;

        double miningRange = cityMiningRange.get();
        double distanceSquared = mc.player.squaredDistanceTo(
            cityBlock.getX() + 0.5, cityBlock.getY() + 0.5, cityBlock.getZ() + 0.5);

        return distanceSquared <= miningRange * miningRange;
    }

    /**
     * Start city mining process
     */
    private void startCityMining(PlayerEntity target, BlockPos cityBlock, FindItemResult pick) {
        cityMiningActive = true;
        cityMiningTarget = target;
        cityMiningBlock = cityBlock;
        cityMiningPick = pick;
        cityMiningProgress = 0.0f;
        cityMiningStarted = false;
        cityMiningStartTime = System.currentTimeMillis();

        // Store current slot for restoration later
        cityMiningPreviousSlot = mc.player.getInventory().selectedSlot;

        // Instantly switch to pickaxe if enabled
        if (cityMiningInstantSwitch.get()) {
            InvUtils.swap(pick.slot(), switchMode.get() == SwitchMode.Silent);
        }

        if (debugMode.get()) {
            info("§aStarted city mining target: " + target.getName().getString() +
                 " at block: " + cityBlock.toShortString());
        }
    }

    /**
     * Validate current city mining state
     */
    private boolean validateCityMiningState() {
        if (cityMiningTarget == null || cityMiningBlock == null || cityMiningPick == null) {
            return false;
        }

        // Check if target is still valid
        if (cityMiningTarget.isRemoved()) {
            return false;
        }

        // Check range
        double miningRange = cityMiningRange.get();
        double distanceSquared = mc.player.squaredDistanceTo(
            cityMiningBlock.getX() + 0.5, cityMiningBlock.getY() + 0.5, cityMiningBlock.getZ() + 0.5);
        if (distanceSquared > miningRange * miningRange) {
            return false;
        }

        // Ensure pickaxe is still available
        List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
        FindItemResult currentPick = InvUtils.find(itemStack -> pickaxeTypes.contains(itemStack.getItem()));
        if (!currentPick.isHotbar()) {
            return false;
        }

        // Update pick reference if needed
        cityMiningPick = currentPick;
        return true;
    }

    /**
     * Continue the city mining process
     */
    private void continueCityMining() {
        // Ensure we're holding the pickaxe
        if (!isHoldingPickaxe()) {
            InvUtils.swap(cityMiningPick.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Rotate to target if needed
        if (rotationMode.get() == RotationMode.Both || rotationMode.get() == RotationMode.Break) {
            Rotations.rotate(Rotations.getYaw(cityMiningBlock), Rotations.getPitch(cityMiningBlock));
        }

        // Start mining if not started
        if (!cityMiningStarted) {
            startMiningBlock();
            cityMiningStarted = true;
            return;
        }

        // Calculate mining progress
        double breakDelta = BlockUtils.getBreakDelta(cityMiningPick.slot(), mc.world.getBlockState(cityMiningBlock));
        cityMiningProgress += (float)breakDelta;

        // Send continuous mining packets if using packet mining
        if (cityMiningPacketMine.get()) {
            sendMiningPackets();
        }

        // Check if block is fully mined
        if (cityMiningProgress >= 1.0f || (cityMiningValidateBreak.get() && isBlockBroken())) {
            finishMining();
        }

        // Apply mining delay
        if (cityMiningDelay.get() > 0) {
            try {
                Thread.sleep(cityMiningDelay.get() * 50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Check if we're currently holding a pickaxe
     */
    private boolean isHoldingPickaxe() {
        Item mainHandItem = mc.player.getMainHandStack().getItem();
        List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
        return pickaxeTypes.contains(mainHandItem);
    }

    /**
     * Start mining the block with proper packets
     */
    private void startMiningBlock() {
        Direction direction = BlockUtils.getDirection(cityMiningBlock);

        if (cityMiningPacketMine.get()) {
            // Send start mining packet
            mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
                PlayerActionC2SPacket.Action.START_DESTROY_BLOCK, cityMiningBlock, direction));
        } else {
            // Use interaction manager
            mc.interactionManager.updateBlockBreakingProgress(cityMiningBlock, direction);
        }

        // Swing hand if enabled
        if (cityMiningSwingHand.get()) {
            mc.player.swingHand(Hand.MAIN_HAND);
        }

        if (debugMode.get()) {
            info("§eStarted mining block at " + cityMiningBlock.toShortString());
        }
    }

    /**
     * Send continuous mining packets
     */
    private void sendMiningPackets() {
        Direction direction = BlockUtils.getDirection(cityMiningBlock);

        // Send abort and restart packets for continuous mining
        mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
            PlayerActionC2SPacket.Action.ABORT_DESTROY_BLOCK, cityMiningBlock, direction));
        mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
            PlayerActionC2SPacket.Action.START_DESTROY_BLOCK, cityMiningBlock, direction));
    }

    /**
     * Check if the block has been broken
     */
    private boolean isBlockBroken() {
        return mc.world.getBlockState(cityMiningBlock).isAir();
    }

    /**
     * Finish mining and clean up
     */
    private void finishMining() {
        Direction direction = BlockUtils.getDirection(cityMiningBlock);

        // Send stop mining packet
        if (cityMiningPacketMine.get()) {
            mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
                PlayerActionC2SPacket.Action.STOP_DESTROY_BLOCK, cityMiningBlock, direction));
        }

        // Final hand swing
        mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));

        if (debugMode.get()) {
            info("§aFinished mining block at " + cityMiningBlock.toShortString());
        }

        // Stop city mining
        stopCityMining();
    }

    /**
     * Stop city mining and restore state
     */
    private void stopCityMining() {
        // Restore previous slot if we switched
        if (cityMiningPreviousSlot != -1 && switchMode.get() == SwitchMode.Silent) {
            InvUtils.swapBack();
        }

        // Reset state
        cityMiningActive = false;
        resetCityMiningState();

        if (debugMode.get()) {
            info("§cStopped city mining");
        }
    }

    private boolean shouldPause() {
        if (pauseOnEat.get() && mc.player.isUsingItem()) {
            Item item = mc.player.getMainHandStack().getItem();
            if (item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE) {
                return true;
            }
        }

        if (pauseOnMine.get() && mc.interactionManager != null && mc.interactionManager.isBreakingBlock()) {
            return true;
        }

        return false;
    }

    private boolean performSafetyChecks() {
        if (!safetyCheck.get()) return true;

        // Emergency health check
        if (emergencyDisable.get()) {
            float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();
            if (currentHealth <= emergencyHealthThreshold.get()) {
                error("Emergency disable triggered - health too low: " + currentHealth);
                toggle();
                return false;
            }
        }

        // Totem safety check
        if (totemSafety.get()) {
            int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
            if (totems < minTotems.get()) {
                return false; // Don't disable, just pause operations
            }
        }

        // Armor safety check
        if (armorSafety.get() && !checkArmorSafety()) {
            return false;
        }

        // Hole safety check
        if (holeSafety.get() && !checkHoleSafety()) {
            return false;
        }

        return true;
    }

    private boolean checkArmorSafety() {
        for (ItemStack armorPiece : mc.player.getArmorItems()) {
            if (armorPiece.isEmpty()) continue;

            if (armorPiece.isDamageable()) {
                int maxDamage = armorPiece.getMaxDamage();
                int currentDamage = armorPiece.getDamage();
                double durabilityPercent = ((double) (maxDamage - currentDamage) / maxDamage) * 100;

                if (durabilityPercent < minArmorDurability.get()) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean checkHoleSafety() {
        BlockPos playerPos = mc.player.getBlockPos();

        // Check if player is in a hole (surrounded by solid blocks)
        if (surroundCheck.get()) {
            BlockPos[] surroundPositions = {
                playerPos.add(1, 0, 0),
                playerPos.add(-1, 0, 0),
                playerPos.add(0, 0, 1),
                playerPos.add(0, 0, -1)
            };

            for (BlockPos pos : surroundPositions) {
                if (!mc.world.getBlockState(pos).isSolidBlock(mc.world, pos)) {
                    return false; // Not fully surrounded
                }
            }
        }

        // Check for solid block below
        if (!mc.world.getBlockState(playerPos.down()).isSolidBlock(mc.world, playerPos.down())) {
            return false;
        }

        return true;
    }

    private boolean isPositionSafe(BlockPos pos) {
        if (!safetyCheck.get()) return true;

        double selfDamage = calculateCrystalDamage(pos, mc.player);

        // Basic safety checks
        if (selfDamage > maxSelfDamage.get()) return false;
        if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) return false;

        // Enhanced safety with totems
        if (totemSafety.get()) {
            int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
            float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();

            // More conservative with fewer totems
            double safetyMultiplier = Math.max(0.5, (double) totems / minTotems.get());
            double adjustedMaxDamage = maxSelfDamage.get() * safetyMultiplier;

            if (selfDamage > adjustedMaxDamage) return false;

            // Don't risk death even with totems if health is very low
            if (currentHealth <= 6 && selfDamage > currentHealth * 0.8) return false;
        }

        // Check if position would put us in danger from other crystals
        if (checkCrystalDangerAtPosition(pos)) return false;

        return true;
    }

    private boolean checkCrystalDangerAtPosition(BlockPos crystalPos) {
        Vec3d crystalVec = Vec3d.ofCenter(crystalPos);
        double totalDanger = 0;

        // Check danger from existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity existingCrystal)) continue;
            if (entity.getPos().distanceTo(crystalVec) > 12) continue;

            double damage = calculateCrystalDamage(existingCrystal.getBlockPos(), mc.player);
            totalDanger += damage;
        }

        // Check danger from potential enemy crystal placements
        if (currentTarget != null) {
            BlockPos targetPos = currentTarget.getBlockPos();
            for (int x = -2; x <= 2; x++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos enemyPos = targetPos.add(x, 0, z);
                    if (isValidCrystalPosition(enemyPos)) {
                        double damage = calculateCrystalDamage(enemyPos, mc.player);
                        totalDanger += damage * 0.3; // Weight potential danger less
                    }
                }
            }
        }

        // Consider total danger unsafe if it exceeds our health
        float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        return totalDanger > currentHealth * 0.9;
    }

   // --- Escape logic supporting chorus fruit and pearl ---
   private boolean shouldUseEscape() {
       if (autoPearlMode.get() == AutoPearlMode.Disabled || pearlCooldownTicks > 0) return false;

       boolean healthLow = false;
       boolean totemsLow = false;

       // Check health
       if (autoPearlMode.get() == AutoPearlMode.Health || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
           float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
           healthLow = health <= pearlHealthThreshold.get();
       }

       // Check totems
       if (autoPearlMode.get() == AutoPearlMode.Totems || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
           int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
           totemsLow = totems <= pearlTotemThreshold.get();
       }

       // Check if being targeted
       if (pearlOnlyWhenTargeted.get() && currentTarget == null) return false;

       boolean trigger = switch (autoPearlMode.get()) {
           case Health -> healthLow;
           case Totems -> totemsLow;
           case Combined -> healthLow && totemsLow;
           case Smart -> healthLow || totemsLow;
           default -> false;
       };

       if (!trigger) return false;

       // Check if escape method is available
       EscapeMethod method = escapeMethod.get();
       boolean hasChorus = InvUtils.find(Items.CHORUS_FRUIT).found();
       boolean hasPearl = InvUtils.find(Items.ENDER_PEARL).found();

       return switch (method) {
           case Chorus -> hasChorus;
           case Pearl -> hasPearl;
           case Both -> hasChorus || hasPearl;
       };
   }

   private void useEscape() {
       EscapeMethod method = escapeMethod.get();
       boolean hasChorus = InvUtils.find(Items.CHORUS_FRUIT).found();
       boolean hasPearl = InvUtils.find(Items.ENDER_PEARL).found();

       // Prioritize chorus fruit if both allowed and available
       if ((method == EscapeMethod.Chorus && hasChorus) || (method == EscapeMethod.Both && hasChorus)) {
           useChorusFruit();
       } else if ((method == EscapeMethod.Pearl && hasPearl) || (method == EscapeMethod.Both && hasPearl)) {
           usePearl();
       }
   }

   // --- Chorus fruit escape logic ---
   private void useChorusFruit() {
       FindItemResult chorus = InvUtils.find(Items.CHORUS_FRUIT);
       if (!chorus.found()) return;

       // Switch to chorus fruit
       if (switchMode.get() != SwitchMode.Disabled) {
           InvUtils.swap(chorus.slot(), switchMode.get() == SwitchMode.Silent);
       }

       // No rotation needed for chorus fruit
       mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
       pearlCooldownTicks = pearlCooldown.get();
       emergencyPearlUsed = true;

       info("Emergency chorus fruit escape activated!");
   }

   // --- Ender pearl escape logic (unchanged) ---
   private void usePearl() {
       FindItemResult pearl = InvUtils.find(Items.ENDER_PEARL);
       if (!pearl.found()) return;

       // Switch to pearl
       if (switchMode.get() != SwitchMode.Disabled) {
           InvUtils.swap(pearl.slot(), switchMode.get() == SwitchMode.Silent);
       }

       // Calculate throw direction (away from enemies)
       Vec3d throwDirection = calculatePearlDirection();
       if (throwDirection == null) return;

       // Rotate if needed
       if (rotationMode.get() != RotationMode.None) {
           float yaw = (float) Math.toDegrees(Math.atan2(throwDirection.z, throwDirection.x)) - 90f;
           float pitch = (float) -Math.toDegrees(Math.asin(throwDirection.y));
           Rotations.rotate(yaw, pitch);
       }

       // Throw pearl
       mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
       pearlCooldownTicks = pearlCooldown.get();
       emergencyPearlUsed = true;

       info("Emergency pearl escape activated!");
   }

    private Vec3d calculatePearlDirection() {
        if (currentTarget == null) return new Vec3d(0, 0, 1); // Default direction

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = currentTarget.getPos();
        Vec3d direction = playerPos.subtract(targetPos).normalize();

        // Add some upward angle for better escape
        return new Vec3d(direction.x, Math.max(0.2, direction.y), direction.z).normalize();
    }

    // === ARMOR MENDING METHODS ===

    /**
     * Check if we should start armor mending
     */
    private boolean shouldStartMending() {
        if (!enableAutoMending.get()) return false;
        if (isMending) return false;

        // Check if we have exp bottles
        FindItemResult expBottles = InvUtils.find(Items.EXPERIENCE_BOTTLE);
        if (!expBottles.found()) return false;

        // Check if any armor piece needs mending
        return needsArmorMending();
    }

    /**
     * Check if we should continue mending
     */
    private boolean shouldContinueMending() {
        if (!enableAutoMending.get()) return false;

        // Check if we still have exp bottles
        FindItemResult expBottles = InvUtils.find(Items.EXPERIENCE_BOTTLE);
        if (!expBottles.found()) return false;

        // Check if any armor piece still needs mending
        return needsArmorMending();
    }

    /**
     * Check if any armor piece needs mending based on thresholds
     */
    private boolean needsArmorMending() {
        ItemStack helmet = mc.player.getInventory().getArmorStack(3);
        ItemStack chestplate = mc.player.getInventory().getArmorStack(2);
        ItemStack leggings = mc.player.getInventory().getArmorStack(1);
        ItemStack boots = mc.player.getInventory().getArmorStack(0);

        return needsMending(helmet, helmetDurability.get(), helmetMaxDurability.get()) ||
               needsMending(chestplate, chestDurability.get(), chestMaxDurability.get()) ||
               needsMending(leggings, leggingsDurability.get(), leggingsMaxDurability.get()) ||
               needsMending(boots, bootsDurability.get(), bootsMaxDurability.get());
    }

    /**
     * Check if a specific armor piece needs mending
     */
    private boolean needsMending(ItemStack armorPiece, double minThreshold, double maxThreshold) {
        if (armorPiece.isEmpty() || !armorPiece.isDamageable()) return false;

        int maxDamage = armorPiece.getMaxDamage();
        int currentDamage = armorPiece.getDamage();
        double durabilityPercent = ((double) (maxDamage - currentDamage) / maxDamage) * 100;

        // Need mending if below minimum threshold and not yet at maximum target
        return durabilityPercent < minThreshold ||
               (isMending && durabilityPercent < maxThreshold);
    }

    /**
     * Start armor mending process
     */
    private void startArmorMending() {
        isMending = true;
        mendingTicks = 0;
        lastExpBottleTime = System.currentTimeMillis();
        expBottlesThrown = 0;

        // Store original rotation for restoration later
        if (mendingRotation.get() && !rotationStored) {
            originalYaw = mc.player.getYaw();
            originalPitch = mc.player.getPitch();
            rotationStored = true;
        }

        if (debugMode.get()) {
            info("§aStarted armor mending - throwing exp bottles");
        }
    }

    /**
     * Stop armor mending process
     */
    private void stopArmorMending() {
        // Restore original rotation if it was stored
        if (mendingRotation.get() && rotationStored) {
            if (mendingServerSideRotation.get()) {
                // Send server-side rotation packet to restore original view
                sendServerRotationPacket(originalYaw, originalPitch);
            } else {
                // Use normal rotation system
                Rotations.rotate(originalYaw, originalPitch);
            }
            rotationStored = false;
        }

        isMending = false;
        mendingTicks = 0;

        if (debugMode.get()) {
            info("§cStopped armor mending - armor repaired or no exp bottles");
        }
    }

    /**
     * Handle armor mending logic
     */
    private void handleArmorMending() {
        if (mendingTicks > 0) {
            mendingTicks--;
            return;
        }

        // Find exp bottles
        FindItemResult expBottles = InvUtils.find(Items.EXPERIENCE_BOTTLE);
        if (!expBottles.found()) {
            stopArmorMending();
            return;
        }

        // Apply mending rotation for optimal exp bottle placement
        if (mendingRotation.get()) {
            applyMendingRotation();
        }

        // Switch to exp bottles if needed
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(expBottles.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Throw exp bottles
        int bottlesToThrow = Math.min(maxExpBottlesPerTick.get(), expBottles.count());
        for (int i = 0; i < bottlesToThrow; i++) {
            throwExpBottle();
            expBottlesThrown++;
        }

        // Set delay for next throw
        mendingTicks = mendingDelay.get();
        lastExpBottleTime = System.currentTimeMillis();

        if (debugMode.get()) {
            info("§eThrew " + bottlesToThrow + " exp bottles (total: " + expBottlesThrown + ")");
        }
    }

    /**
     * Apply mending rotation based on the selected mode
     */
    private void applyMendingRotation() {
        float targetYaw = mc.player.getYaw();
        float targetPitch = 0f;

        switch (mendingRotationMode.get()) {
            case LookUp:
                targetPitch = -90f; // Look straight up
                break;
            case LookDown:
                targetPitch = 90f; // Look straight down
                break;
            case LookAtFeet:
                // Calculate angle to look at our feet
                Vec3d playerPos = mc.player.getPos();
                Vec3d feetPos = playerPos.add(0, -1.8, 0); // Approximate feet position
                Vec3d direction = feetPos.subtract(playerPos.add(0, mc.player.getEyeHeight(mc.player.getPose()), 0));

                targetYaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
                targetPitch = (float) Math.toDegrees(Math.asin(-direction.normalize().y));
                break;
        }

        // Apply rotation based on server-side setting
        if (mendingServerSideRotation.get()) {
            sendServerRotationPacket(targetYaw, targetPitch);
        } else {
            Rotations.rotate(targetYaw, targetPitch);
        }
    }

    /**
     * Send server-side rotation packet without affecting client view
     */
    private void sendServerRotationPacket(float yaw, float pitch) {
        if (mc.player == null || mc.getNetworkHandler() == null) return;

        // Send rotation packet to server with correct parameters
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.LookAndOnGround(
            yaw, pitch, mc.player.isOnGround(), false));

        if (debugMode.get()) {
            info("§7Sent server rotation: yaw=" + String.format("%.1f", yaw) +
                 ", pitch=" + String.format("%.1f", pitch));
        }
    }

    /**
     * Throw an experience bottle
     */
    private void throwExpBottle() {
        if (mc.player == null || mc.interactionManager == null) return;

        // Use the item (throw exp bottle)
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);

        // Swing hand for visual feedback
        mc.player.swingHand(Hand.MAIN_HAND);
    }

    /**
     * Get durability percentage of an item stack
     */
    private double getDurabilityPercent(ItemStack stack) {
        if (!stack.isDamageable()) return 100.0;

        int maxDamage = stack.getMaxDamage();
        int currentDamage = stack.getDamage();
        return ((double) (maxDamage - currentDamage) / maxDamage) * 100;
    }

    private void findTarget() {
        potentialTargets.clear();

        // Collect all valid targets
        for (Entity entity : mc.world.getEntities()) {
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > targetRange.get()) continue;

            potentialTargets.add(entity);
        }

        if (potentialTargets.isEmpty()) {
            currentTarget = null;
            predictedTargetPos = null;
            return;
        }

        // Advanced targeting logic
        Entity newTarget = smartTargeting.get() ?
            findSmartTarget() : findBasicTarget();

        // Check if we should switch targets
        if (targetSwitching.get() && currentTarget != null && newTarget != currentTarget) {
            if (!shouldSwitchTarget(newTarget)) {
                newTarget = currentTarget;
            }
        }

        // Update target and prediction
        if (newTarget != currentTarget) {
            lastTarget = currentTarget;
            currentTarget = newTarget;
        }

        // Update movement prediction
        if (currentTarget != null && predictiveTargeting.get()) {
            updateTargetPrediction();
        }
    }

    private Entity findSmartTarget() {
        if (potentialTargets.isEmpty()) return null;

        Map<Entity, Double> targetScores = new HashMap<>();

        for (Entity entity : potentialTargets) {
            double score = calculateAdvancedTargetScore(entity);
            targetScores.put(entity, score);
        }

        // Return target with highest score
        return targetScores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
    }

    private Entity findBasicTarget() {
        if (potentialTargets.isEmpty()) return null;

        Entity bestTarget = null;
        double bestScore = Double.MAX_VALUE;

        for (Entity entity : potentialTargets) {
            double distance = mc.player.distanceTo(entity);
            double score = calculateTargetScore(entity, distance);

            if (score < bestScore) {
                bestScore = score;
                bestTarget = entity;
            }
        }

        return bestTarget;
    }

    private boolean shouldSwitchTarget(Entity newTarget) {
        if (currentTarget == null || newTarget == null) return true;

        double currentDamage = calculateMaxDamageToTarget(currentTarget);
        double newDamage = calculateMaxDamageToTarget(newTarget);

        // Switch if new target offers significantly more damage
        return newDamage > currentDamage + switchThreshold.get();
    }

    private double calculateAdvancedTargetScore(Entity entity) {
        double score = 0;

        // Base factors
        double distance = mc.player.distanceTo(entity);
        double health = entity instanceof LivingEntity living ? living.getHealth() : 20f;
        double maxDamage = calculateMaxDamageToTarget(entity);

        // Distance factor (closer is better, but not too close for safety)
        double optimalDistance = 6.0;
        double distanceFactor = Math.abs(distance - optimalDistance);
        score -= distanceFactor * 2;

        // Damage potential factor
        score += maxDamage * 10;

        // Health factor (prioritize low health targets)
        score += (20 - health) * 3;

        // Movement prediction factor
        if (predictiveTargeting.get()) {
            Vec3d velocity = entity.getVelocity();
            double speed = velocity.length();

            // Penalize fast-moving targets slightly
            score -= speed * 5;

            // Bonus for predictable movement
            if (speed > 0.1 && speed < 0.5) {
                score += 10; // Moderate speed is easier to predict
            }
        }

        // Armor factor for players
        if (entity instanceof PlayerEntity player) {
            int armorValue = getArmorValue(player);

            switch (targetPriority.get()) {
                case MostArmor -> score += armorValue * 5;
                case LeastArmor -> score -= armorValue * 5;
                default -> score -= armorValue * 2; // Generally prefer less armored targets
            }
        }

        // Face place bonus
        if (facePlaceMode.get() && health <= facePlaceHealth.get()) {
            score += 50; // High bonus for face place targets
        }

        // Continuity bonus (prefer current target to reduce switching)
        if (entity == currentTarget) {
            score += 15;
        }

        return score;
    }

    private double calculateMaxDamageToTarget(Entity target) {
        if (target == null) return 0;

        double maxDamage = 0;
        Vec3d targetPos = predictiveTargeting.get() && predictedTargetPos != null ?
            predictedTargetPos : target.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        // Check damage from potential crystal positions around target
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = targetBlockPos.add(x, y, z);

                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
                    if (!isValidCrystalPosition(pos)) continue;

                    double damage = calculateCrystalDamage(pos, target);
                    double selfDamage = calculateCrystalDamage(pos, mc.player);

                    if (selfDamage > maxSelfDamage.get()) continue;
                    if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;

                    maxDamage = Math.max(maxDamage, damage);
                }
            }
        }

        return maxDamage;
    }

    private void updateTargetPrediction() {
        if (currentTarget == null) {
            predictedTargetPos = null;
            return;
        }

        Vec3d currentPos = currentTarget.getPos();
        Vec3d velocity = currentTarget.getVelocity();

        // Simple linear prediction
        double predictionTime = predictionTicks.get();

        // Adjust prediction based on accuracy setting
        double accuracy = predictionAccuracy.get();
        predictionTime *= accuracy;

        // Account for acceleration/deceleration
        Vec3d acceleration = Vec3d.ZERO;
        if (lastTarget == currentTarget && predictedTargetPos != null) {
            // Calculate acceleration from previous prediction
            Vec3d expectedPos = predictedTargetPos;
            Vec3d actualPos = currentPos;
            Vec3d error = actualPos.subtract(expectedPos);

            // Use error to improve next prediction
            acceleration = error.multiply(0.1); // Small correction factor
        }

        // Calculate predicted position
        predictedTargetPos = currentPos
            .add(velocity.multiply(predictionTime))
            .add(acceleration.multiply(predictionTime * predictionTime * 0.5));

        // Clamp prediction to reasonable bounds
        double maxPredictionDistance = targetRange.get() * 1.5;
        if (currentPos.distanceTo(predictedTargetPos) > maxPredictionDistance) {
            Vec3d direction = predictedTargetPos.subtract(currentPos).normalize();
            predictedTargetPos = currentPos.add(direction.multiply(maxPredictionDistance));
        }
    }

    private boolean isValidTarget(Entity entity) {
        if (!(entity instanceof LivingEntity living)) return false;
        if (entity == mc.player) return false;
        if (!targetEntities.get().contains(entity.getType())) return false;
        if (entity.isRemoved() || !entity.isAlive()) return false;

        if (entity instanceof PlayerEntity player && Friends.get().isFriend(player)) return false;

        if (ignoreNaked.get() && entity instanceof PlayerEntity player) {
            // Check if player has armor
            boolean hasArmor = false;
            for (ItemStack stack : player.getArmorItems()) {
                if (!stack.isEmpty()) {
                    hasArmor = true;
                    break;
                }
            }
            if (!hasArmor) return false;
        }

        return true;
    }

    private double calculateTargetScore(Entity entity, double distance) {
        return switch (targetPriority.get()) {
            case Closest -> distance;
            case LowestHealth -> entity instanceof LivingEntity living ? living.getHealth() : distance;
            case HighestDamage -> -calculateDamageToTarget(entity); // Negative for highest
            case MostArmor -> entity instanceof PlayerEntity player ? -getArmorValue(player) : distance;
            case LeastArmor -> entity instanceof PlayerEntity player ? getArmorValue(player) : distance;
        };
    }

    private double calculateDamageToTarget(Entity target) {
        // Find closest crystal position to target for damage calculation
        BlockPos targetPos = target.getBlockPos();
        BlockPos crystalPos = targetPos.add(1, 0, 0); // Simplified
        return calculateCrystalDamage(crystalPos, target);
    }

    private double calculateCrystalDamage(BlockPos crystalPos, Entity target) {
        if (target == null) return 0;

        Vec3d crystalVec = Vec3d.ofCenter(crystalPos);
        Vec3d targetVec = target.getPos();

        // Calculate distance
        double distance = crystalVec.distanceTo(targetVec);
        if (distance > 12) return 0; // Max crystal damage range

        // Base damage calculation (simplified)
        double damage = 12 * (1 - (distance / 12));

        // Apply armor reduction if it's a player
        if (target instanceof PlayerEntity player) {
            int armor = getArmorValue(player);
            damage = damage * (1 - (armor * 0.04)); // 4% reduction per armor point
        }

        // Ensure minimum damage
        return Math.max(0, damage);
    }

    private int getArmorValue(PlayerEntity player) {
        int armor = 0;
        for (ItemStack stack : player.getArmorItems()) {
            if (stack.getItem() instanceof ArmorItem) {
                armor += 1; // Simplified armor counting
            }
        }
        return armor;
    }

    private void handleTrapping() {
        if (!enableTrapping.get() || currentTarget == null) return;
        if (isTrapping) return; // Prevent concurrent trapping

        // Check if we should trap this target
        if (trapOnlyLowHealth.get()) {
            if (!(currentTarget instanceof LivingEntity living) ||
                living.getHealth() > trapHealthThreshold.get()) {
                return;
            }
        }

        trapBlockPositions.clear();
        findTrapPositions();

        if (!trapBlockPositions.isEmpty()) {
            isTrapping = true;
            placeTrapBlocks();
            isTrapping = false;
        }
    }

    private void findTrapPositions() {
        if (currentTarget == null) return;

        Vec3d targetPos = predictMovement.get() && predictedTargetPos != null ?
            predictedTargetPos : currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        List<BlockPos> candidates = new ArrayList<>();

        // Find positions around target for trapping
        int range = trapRange.get().intValue();
        for (int x = -range; x <= range; x++) {
            for (int z = -range; z <= range; z++) {
                for (int y = -1; y <= 3; y++) {
                    BlockPos pos = targetBlockPos.add(x, y, z);

                    // Skip positions too far from player
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;

                    // Check if this is a valid trap position
                    if (isValidTrapPosition(pos, targetBlockPos)) {
                        candidates.add(pos);
                    }
                }
            }
        }

        // Select best trap positions
        selectBestTrapPositions(candidates, targetBlockPos);
    }

    private boolean isValidTrapPosition(BlockPos pos, BlockPos targetPos) {
        // Must be air block
        if (!mc.world.getBlockState(pos).isAir()) return false;

        // Must have solid block below (for placement)
        if (mc.world.getBlockState(pos.down()).isAir()) return false;

        // Check if position would actually trap the target
        double distance = pos.getSquaredDistance(targetPos);
        if (distance > trapRange.get() * trapRange.get()) return false;

        // Prioritize positions that block movement
        if (trapSides.get()) {
            // Check if this position blocks horizontal movement
            int dx = pos.getX() - targetPos.getX();
            int dz = pos.getZ() - targetPos.getZ();
            if (Math.abs(dx) <= 1 && Math.abs(dz) <= 1 && pos.getY() == targetPos.getY()) {
                return true;
            }
        }

        if (trapAbove.get()) {
            // Check if this position blocks vertical movement
            if (pos.getX() == targetPos.getX() && pos.getZ() == targetPos.getZ() &&
                pos.getY() > targetPos.getY() && pos.getY() <= targetPos.getY() + 2) {
                return true;
            }
        }

        return false;
    }

    private void selectBestTrapPositions(List<BlockPos> candidates, BlockPos targetPos) {
        if (candidates.isEmpty()) return;

        Map<BlockPos, Double> positionScores = new HashMap<>();
        Vec3d targetVelocity = currentTarget.getVelocity();

        for (BlockPos pos : candidates) {
            double score = calculateTrapScore(pos, targetPos, targetVelocity);
            if (score > 0) {
                positionScores.put(pos, score);
            }
        }

        // Sort by score and select top positions
        List<Map.Entry<BlockPos, Double>> sortedPositions = positionScores.entrySet()
            .stream()
            .sorted(Map.Entry.<BlockPos, Double>comparingByValue().reversed())
            .toList();

        int maxBlocks = Math.min(maxTrapBlocks.get(), sortedPositions.size());
        for (int i = 0; i < maxBlocks; i++) {
            trapBlockPositions.add(sortedPositions.get(i).getKey());
        }
    }

    private double calculateTrapScore(BlockPos pos, BlockPos targetPos, Vec3d targetVelocity) {
        double score = 0;

        // Base score - closer to target is better
        double distance = Math.sqrt(pos.getSquaredDistance(targetPos));
        score += (trapRange.get() - distance) * 10;

        // Bonus for blocking escape routes
        if (smartTrapping.get() && targetVelocity.lengthSquared() > 0.01) {
            Vec3d escapeDirection = targetVelocity.normalize();
            Vec3d posDirection = Vec3d.ofCenter(pos).subtract(Vec3d.ofCenter(targetPos)).normalize();
            double alignment = posDirection.dotProduct(escapeDirection);
            if (alignment > 0.5) {
                score += 50; // High bonus for blocking escape
            }
        }

        // Bonus for strategic positions
        int dx = Math.abs(pos.getX() - targetPos.getX());
        int dz = Math.abs(pos.getZ() - targetPos.getZ());
        int dy = pos.getY() - targetPos.getY();

        // Prioritize positions that form walls
        if (dx <= 1 && dz <= 1 && dy == 0) score += 30; // Side blocking
        if (dx == 0 && dz == 0 && dy > 0 && dy <= 2) score += 40; // Above blocking

        // Penalty for positions that might block our own crystals
        for (BlockPos crystalPos : multiCrystalPositions) {
            if (pos.isWithinDistance(crystalPos, 2)) {
                score -= 20;
            }
        }

        return score;
    }

    private void placeTrapBlocks() {
        FindItemResult obsidian = InvUtils.find(Items.OBSIDIAN);
        if (!obsidian.found()) {
            // Try other solid blocks
            obsidian = InvUtils.find(Items.COBBLESTONE, Items.STONE, Items.NETHERRACK);
            if (!obsidian.found()) return;
        }

        for (BlockPos pos : trapBlockPositions) {
            if (!canPlaceBlockBPS()) continue;
            if (placeTrapBlock(pos, obsidian)) {
                // Small delay between trap block placements
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    private boolean placeTrapBlock(BlockPos pos, FindItemResult blockItem) {
        // Switch to block
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Rotate towards position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d blockVec = Vec3d.ofCenter(pos);
            Vec3d playerVec = mc.player.getEyePos();
            Vec3d direction = blockVec.subtract(playerVec).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Place the block
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        return mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result).isAccepted();
    }

    private void handlePlacement() {
        if (placeTicks > 0) return;

        multiCrystalPositions.clear();

        if (multiCrystalMode.get() == MultiCrystalMode.Disabled) {
            // Single crystal placement
            BlockPos pos = findBestCrystalPosition();
            if (pos != null) {
                multiCrystalPositions.add(pos);
                info("Found crystal position: " + pos.toString());
            } else {
                info("No valid crystal position found");
            }
        } else {
            // Multi-crystal placement
            findMultiCrystalPositions();
            info("Multi-crystal positions found: " + multiCrystalPositions.size());
        }

        // Place crystals
        for (BlockPos pos : multiCrystalPositions) {
            info("Attempting to place crystal at: " + pos.toString());
            if (placeCrystalWithHand(pos)) {
                info("Crystal placed successfully!");
                placeTicks = placeDelay.get();
                if (multiCrystalDelay.get() > 0) {
                    try {
                        Thread.sleep(multiCrystalDelay.get() * 50); // Convert ticks to ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } else {
                info("Failed to place crystal");
            }
        }
    }

    private void handleBreaking() {
        if (breakTicks > 0) return;

        targetCrystals.clear();
        burstTargets.clear();

        // Find all crystals in range
        findTargetCrystals();

        if (targetCrystals.isEmpty()) return;

        // Prioritize crystals for breaking
        prioritizeCrystals();

        if (burstBreaking.get() && !burstTargets.isEmpty()) {
            performBurstBreaking();
        } else {
            // Standard breaking
            for (EndCrystalEntity crystal : targetCrystals) {
                if (shouldBreakCrystal(crystal) && breakCrystal(crystal)) {
                    breakTicks = breakDelay.get();
                    burstBreakCount++;
                    break; // Break one at a time in standard mode
                }
            }
        }
    }

    private void findTargetCrystals() {
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity crystal)) continue;
            if (mc.player.distanceTo(crystal) > breakRange.get()) continue;
            if (crystal.isRemoved()) continue;

            // Check if crystal is worth breaking
            if (shouldBreakCrystal(crystal)) {
                targetCrystals.add(crystal);
            }
        }
    }

    private boolean shouldBreakCrystal(EndCrystalEntity crystal) {
        // Inhibit mode: prioritize defensive breaking
        if (inhibit.get()) {
            // Check if this crystal poses a threat to us
            double selfDamage = calculateCrystalDamage(crystal.getBlockPos(), mc.player);

            // Instantly break crystals that could kill us or deal significant damage
            if (selfDamage >= mc.player.getHealth() * 0.8 || selfDamage > maxSelfDamage.get() * 0.5) {
                return true; // Defensive breaking - ignore other conditions
            }

            // Break crystals placed by opponents near us
            if (mc.player.distanceTo(crystal) <= breakRange.get() * 0.7) {
                return true; // Close range defensive breaking
            }
        }

        // Standard breaking logic
        // Check damage to current target
        double damageToTarget = 0;
        if (currentTarget != null) {
            damageToTarget = calculateCrystalDamage(crystal.getBlockPos(), currentTarget);
        }

        // Check damage to any valid targets if no current target
        if (damageToTarget < minDamageBreak.get()) {
            for (Entity entity : mc.world.getEntities()) {
                if (isValidTarget(entity)) {
                    double damage = calculateCrystalDamage(crystal.getBlockPos(), entity);
                    if (damage >= minDamageBreak.get()) {
                        damageToTarget = damage;
                        break;
                    }
                }
            }
        }

        // Check if crystal deals enough damage (skip for inhibit mode)
        if (!inhibit.get() && damageToTarget < minDamageBreak.get()) return false;

        // Check self damage (more lenient in inhibit mode)
        double selfDamage = calculateCrystalDamage(crystal.getBlockPos(), mc.player);
        if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) return false;
        if (!inhibit.get() && selfDamage > maxSelfDamage.get()) return false;

        // Check break attempts
        int attempts = crystalBreakAttempts.getOrDefault(crystal.getId(), 0);
        if (attempts >= breakAttempts.get()) return false;

        // Check timing for packet breaking (more aggressive in inhibit mode)
        if (packetBreaking.get()) {
            long lastAttack = crystalLastAttack.getOrDefault(crystal.getId(), 0L);
            long minDelay = inhibit.get() ? 25 : 50; // Faster breaking in inhibit mode
            if (System.currentTimeMillis() - lastAttack < minDelay) return false;
        }

        return true;
    }

    private void prioritizeCrystals() {
        // Sort crystals by priority: damage to target, proximity to opponents, then distance to player
        targetCrystals.sort((a, b) -> {
            double damageA = currentTarget != null ? calculateCrystalDamage(a.getBlockPos(), currentTarget) : 0;
            double damageB = currentTarget != null ? calculateCrystalDamage(b.getBlockPos(), currentTarget) : 0;

            // Compute proximity score: lower is better (closer to any valid opponent)
            double proximityA = getNearestOpponentDistance(a);
            double proximityB = getNearestOpponentDistance(b);

            // Higher damage first
            if (Math.abs(damageA - damageB) > 1.0) {
                return Double.compare(damageB, damageA);
            }

            // If damage is similar, prioritize crystals closer to opponents
            if (Math.abs(proximityA - proximityB) > 0.5) {
                return Double.compare(proximityA, proximityB);
            }

            // If still similar, prioritize closer to player
            double distA = mc.player.distanceTo(a);
            double distB = mc.player.distanceTo(b);
            return Double.compare(distA, distB);
        });

        // Select crystals for burst breaking
        if (burstBreaking.get()) {
            int burstSize = Math.min(burstCount.get(), targetCrystals.size());
            for (int i = 0; i < burstSize; i++) {
                burstTargets.add(targetCrystals.get(i));
            }
        }
    }

    // Returns the distance from the crystal to the nearest valid opponent (excluding self)
    private double getNearestOpponentDistance(EndCrystalEntity crystal) {
        double minDist = Double.MAX_VALUE;
        Vec3d crystalPos = crystal.getPos();
        for (Entity entity : mc.world.getEntities()) {
            if (isValidTarget(entity)) {
                double dist = crystalPos.distanceTo(entity.getPos());
                if (dist < minDist) minDist = dist;
            }
        }
        return minDist;
    }

    private void performBurstBreaking() {
        if (burstTargets.isEmpty()) return;

        long currentTime = System.currentTimeMillis();

        // Check if we can perform burst (timing)
        if (currentTime - lastBreakTime < burstDelay.get()) return;

        // Switch to appropriate tool if needed
        if (antiWeakness.get() && mc.player.hasStatusEffect(StatusEffects.WEAKNESS)) {
            FindItemResult tool = InvUtils.find(itemStack ->
                itemStack.getItem() instanceof SwordItem ||
                itemStack.getItem() instanceof AxeItem ||
                itemStack.getItem() instanceof PickaxeItem
            );
            if (tool.found() && switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(tool.slot(), switchMode.get() == SwitchMode.Silent);
            }
        }

        // Perform burst attacks
        for (EndCrystalEntity crystal : burstTargets) {
            if (crystal.isRemoved()) continue;

            if (packetBreaking.get()) {
                performPacketBreak(crystal);
            } else {
                performStandardBreak(crystal);
            }

            // Update tracking
            crystalBreakAttempts.put(crystal.getId(),
                crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
            crystalLastAttack.put(crystal.getId(), currentTime);

            burstBreakCount++;

            // Small delay between burst attacks
            if (burstDelay.get() > 0) {
                try {
                    Thread.sleep(burstDelay.get() / burstTargets.size());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        lastBreakTime = currentTime;
        breakTicks = breakDelay.get();
    }

    /**
     * Finds the best crystal position, prioritizing positions directly adjacent to the opponent.
     * If no adjacent positions are valid, selects the next closest valid position.
     */
    private BlockPos findBestCrystalPosition() {
        if (currentTarget == null) return null;
    
        BlockPos targetPos = currentTarget.getBlockPos();
        BlockPos bestPos = null;
        double bestDamage = 0;
    
        // 1. Check all directly adjacent positions (N, S, E, W, Up, Down)
        BlockPos[] adjacentOffsets = {
            targetPos.add(1, 0, 0),
            targetPos.add(-1, 0, 0),
            targetPos.add(0, 0, 1),
            targetPos.add(0, 0, -1),
            targetPos.add(0, 1, 0),
            targetPos.add(0, -1, 0)
        };
    
        for (BlockPos pos : adjacentOffsets) {
            if (!isValidCrystalPosition(pos)) continue;
            if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
    
            double damage = calculateCrystalDamage(pos, currentTarget);
            double selfDamage = calculateCrystalDamage(pos, mc.player);
    
            if (damage < minDamagePlace.get()) continue;
            if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
            if (selfDamage > maxSelfDamage.get()) continue;
    
            if (damage > bestDamage) {
                bestDamage = damage;
                bestPos = pos;
            }
        }
    
        // If a valid adjacent position was found, return it
        if (bestPos != null) return bestPos;
    
        // 2. If no adjacent positions are valid, search all positions within range and pick the closest valid one
        double closestDist = Double.MAX_VALUE;
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = targetPos.add(x, y, z);
    
                    if (!isValidCrystalPosition(pos)) continue;
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
    
                    double damage = calculateCrystalDamage(pos, currentTarget);
                    double selfDamage = calculateCrystalDamage(pos, mc.player);
    
                    if (damage < minDamagePlace.get()) continue;
                    if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
                    if (selfDamage > maxSelfDamage.get()) continue;
    
                    double dist = pos.getSquaredDistance(targetPos);
                    if (dist < closestDist) {
                        closestDist = dist;
                        bestDamage = damage;
                        bestPos = pos;
                    }
                }
            }
        }
    
        return bestPos;
    }

    /**
     * Finds multi-crystal positions, prioritizing positions directly adjacent to the opponent first.
     * If not enough adjacent positions are valid, fills remaining slots with the next closest valid positions.
     */
    private void findMultiCrystalPositions() {
        if (currentTarget == null) return;
    
        multiCrystalPositions.clear();
        positionDamageMap.clear();
    
        int targetCount = calculateTargetCrystalCount();
        if (targetCount <= 1) {
            // Single crystal mode
            BlockPos bestPos = findBestCrystalPosition();
            if (bestPos != null) {
                multiCrystalPositions.add(bestPos);
            }
            return;
        }
    
        // 1. Collect all valid adjacent positions first
        BlockPos targetPos = currentTarget.getBlockPos();
        BlockPos[] adjacentOffsets = {
            targetPos.add(1, 0, 0),
            targetPos.add(-1, 0, 0),
            targetPos.add(0, 0, 1),
            targetPos.add(0, 0, -1),
            targetPos.add(0, 1, 0),
            targetPos.add(0, -1, 0)
        };
        for (BlockPos pos : adjacentOffsets) {
            if (multiCrystalPositions.size() >= targetCount) break;
            if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
            double damage = calculateCrystalDamage(pos, currentTarget);
            if (damage >= minDamagePlace.get()) {
                multiCrystalPositions.add(pos);
                positionDamageMap.put(pos, damage);
            }
        }
    
        // 2. If not enough, fill with closest valid positions
        if (multiCrystalPositions.size() < targetCount) {
            List<BlockPos> candidatePositions = findCandidatePositions();
            // Sort by distance to target
            candidatePositions.sort(Comparator.comparingDouble(p -> p.getSquaredDistance(targetPos)));
            for (BlockPos pos : candidatePositions) {
                if (multiCrystalPositions.size() >= targetCount) break;
                if (multiCrystalPositions.contains(pos)) continue;
                if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
                double damage = calculateCrystalDamage(pos, currentTarget);
                if (damage >= minDamagePlace.get()) {
                    multiCrystalPositions.add(pos);
                    positionDamageMap.put(pos, damage);
                }
            }
        }
    
        // If we still don't have enough positions, try fallback patterns
        if (multiCrystalPositions.size() < targetCount) {
            findFallbackPositions(targetCount - multiCrystalPositions.size());
        }
    }

    private int calculateTargetCrystalCount() {
        return switch (multiCrystalMode.get()) {
            case Disabled -> 1;
            case Dual -> 2;
            case Triple -> 3;
            case Quad -> 4;
            case Penta -> 5;
            case Burst -> Math.min(maxCrystals.get(), 5);
            case Adaptive -> calculateAdaptiveCount();
        };
    }

    private int calculateAdaptiveCount() {
        if (currentTarget == null) return 2;

        double distance = mc.player.distanceTo(currentTarget);
        float targetHealth = currentTarget instanceof LivingEntity living ? living.getHealth() : 20f;

        // Close combat with low health target - use maximum crystals
        if (distance < 4 && targetHealth < 10) return Math.min(maxCrystals.get(), 5);

        // Medium range combat - use moderate crystal count
        if (distance < 8) return Math.min(maxCrystals.get(), 3);

        // Long range - use fewer crystals for precision
        return Math.min(maxCrystals.get(), 2);
    }

    private List<BlockPos> findCandidatePositions() {
        List<BlockPos> candidates = new ArrayList<>();
        if (currentTarget == null) return candidates;

        Vec3d targetPos = predictMovement.get() && predictedTargetPos != null ?
            predictedTargetPos : currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        // Search in expanding radius around target
        int searchRadius = (int) Math.ceil(placeRange.get());
        for (int radius = 1; radius <= searchRadius; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    for (int y = -2; y <= 3; y++) {
                        if (Math.abs(x) != radius && Math.abs(z) != radius && y != -2 && y != 3) continue;

                        BlockPos pos = targetBlockPos.add(x, y, z);
                        if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) <= placeRange.get()) {
                            candidates.add(pos);
                        }
                    }
                }
            }
        }

        return candidates;
    }

    private List<BlockPos> selectOptimalPositions(List<BlockPos> candidates, int targetCount) {
        List<BlockPos> selected = new ArrayList<>();
        Map<BlockPos, Double> damageScores = new HashMap<>();
    
        // Calculate damage scores for all candidates
        for (BlockPos pos : candidates) {
            if (!isValidCrystalPosition(pos)) continue;
    
            double damage = calculateCrystalDamage(pos, currentTarget);
            double selfDamage = calculateCrystalDamage(pos, mc.player);
    
            if (damage < minDamagePlace.get()) continue;
            if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
            if (selfDamage > maxSelfDamage.get()) continue;
    
            // Score based on damage to target minus self damage
            double score = damage - (selfDamage * 0.5);
            damageScores.put(pos, score);
        }
    
        // Sort by proximity to opponent's feet, then by damage score
        BlockPos targetFeet = currentTarget != null ? currentTarget.getBlockPos() : BlockPos.ORIGIN;
        List<Map.Entry<BlockPos, Double>> sortedEntries = damageScores.entrySet()
            .stream()
            .sorted((a, b) -> {
                int yComp = Integer.compare(a.getKey().getY(), b.getKey().getY());
                if (yComp != 0) return yComp;
                double da = a.getKey().getSquaredDistance(targetFeet);
                double db = b.getKey().getSquaredDistance(targetFeet);
                if (da != db) return Double.compare(da, db);
                return Double.compare(b.getValue(), a.getValue());
            })
            .toList();
    
        // Select positions avoiding overlap if enabled
        for (Map.Entry<BlockPos, Double> entry : sortedEntries) {
            if (selected.size() >= targetCount) break;
    
            BlockPos pos = entry.getKey();
            boolean shouldAdd = true;
    
            if (avoidOverlap.get()) {
                for (BlockPos existing : selected) {
                    if (pos.isWithinDistance(existing, 4.0)) {
                        // Check if this position provides significantly more damage
                        double existingDamage = damageScores.getOrDefault(existing, 0.0);
                        double newDamage = entry.getValue();
                        if (newDamage <= existingDamage * 1.2) {
                            shouldAdd = false;
                            break;
                        }
                    }
                }
            }
    
            if (shouldAdd) {
                selected.add(pos);
            }
        }
    
        return selected;
    }

    private List<BlockPos> applyPlacementPattern(List<BlockPos> positions, int targetCount) {
        if (positions.size() <= 1 || currentTarget == null) return positions;

        Vec3d targetPos = currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        return switch (placementPattern.get()) {
            case Surrounding -> arrangeSurrounding(positions, targetBlockPos, targetCount);
            case Linear -> arrangeLinear(positions, targetBlockPos, targetCount);
            case Cross -> arrangeCross(positions, targetBlockPos, targetCount);
            case Diamond -> arrangeDiamond(positions, targetBlockPos, targetCount);
            case Trap -> arrangeTrap(positions, targetBlockPos, targetCount);
            case Optimal -> positions; // Already optimized by damage
        };
    }



    private void findFallbackPositions(int needed) {
        if (currentTarget == null || needed <= 0) return;

        BlockPos targetPos = currentTarget.getBlockPos();

        // Simple fallback - find any valid positions near target
        for (int radius = 1; radius <= 4 && multiCrystalPositions.size() < maxCrystals.get(); radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    if (multiCrystalPositions.size() >= maxCrystals.get()) return;

                    BlockPos pos = targetPos.add(x, 0, z);
                    if (isValidCrystalPosition(pos) && isPositionSafe(pos)) {
                        double damage = calculateCrystalDamage(pos, currentTarget);
                        if (damage >= minDamagePlace.get() * 0.7) { // Lower threshold for fallback
                            multiCrystalPositions.add(pos);
                            positionDamageMap.put(pos, damage);
                            needed--;
                            if (needed <= 0) return;
                        }
                    }
                }
            }
        }
    }

    private boolean isValidCrystalPosition(BlockPos pos) {
        // Check if position is valid for crystal placement
        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;

        // User-configurable base block check
        BlockPos basePos = pos.down();
        Block baseBlock = mc.world.getBlockState(basePos).getBlock();
        if (!validCrystalBaseBlocks.get().contains(baseBlock)) return false;

        // Check for existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof EndCrystalEntity && entity.getBlockPos().equals(pos)) {
                return false;
            }
        }

        // Check if we can place here (1.12 placement check)
        if (!placement112.get()) {
            // 1.13+ placement - check for entities in the way
            Box box = new Box(pos.getX(), pos.getY(), pos.getZ(), pos.getX() + 1, pos.getY() + 2, pos.getZ() + 1);
            if (!mc.world.getOtherEntities(null, box).isEmpty()) return false;
        }

        return true;
    }

    /**
     * Place a crystal at the given position using the correct hand (main/offhand).
     * Returns true if placement was attempted.
     */
    private boolean placeCrystalWithHand(BlockPos pos) {
        Hand handToUse = null;
        ItemStack mainHandStack = mc.player.getMainHandStack();
        ItemStack offHandStack = mc.player.getOffHandStack();

        if (mainHandStack.getItem() == Items.END_CRYSTAL) {
            handToUse = Hand.MAIN_HAND;
        } else if (offHandStack.getItem() == Items.END_CRYSTAL) {
            handToUse = Hand.OFF_HAND;
        } else {
            // Fallback: try to swap to crystal in hotbar
            FindItemResult crystal = InvUtils.find(Items.END_CRYSTAL);
            if (!crystal.found()) return false;
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(crystal.slot(), switchMode.get() == SwitchMode.Silent);
                if (switchDelay.get() > 0) {
                    switchTicks = switchDelay.get();
                    return false; // Wait for switch delay
                }
            }
            handToUse = Hand.MAIN_HAND;
        }

        // Rotate towards position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d crystalVec = Vec3d.ofCenter(pos);
            Vec3d playerVec = mc.player.getEyePos();
            Vec3d direction = crystalVec.subtract(playerVec).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Place the crystal
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        mc.interactionManager.interactBlock(mc.player, handToUse, result);
        return true;
    }

    private boolean breakCrystal(EndCrystalEntity crystal) {
        if (packetBreaking.get()) {
            return performPacketBreak(crystal);
        } else {
            return performStandardBreak(crystal);
        }
    }

    private boolean performPacketBreak(EndCrystalEntity crystal) {
        // Rotate if needed
        if (rotationMode.get() == RotationMode.Break || rotationMode.get() == RotationMode.Both) {
            if (!rotateToCrystal(crystal)) return false;
        }

        // Send attack packet directly for maximum speed
        mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(crystal, mc.player.isSneaking()));

        // Update tracking
        crystalBreakAttempts.put(crystal.getId(),
            crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
        crystalLastAttack.put(crystal.getId(), System.currentTimeMillis());

        return true;
    }

    private boolean performStandardBreak(EndCrystalEntity crystal) {
        // Rotate if needed
        if (rotationMode.get() == RotationMode.Break || rotationMode.get() == RotationMode.Both) {
            if (!rotateToCrystal(crystal)) return false;
        }

        // Standard attack through interaction manager
        mc.interactionManager.attackEntity(mc.player, crystal);

        // Update tracking
        crystalBreakAttempts.put(crystal.getId(),
            crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
        crystalLastAttack.put(crystal.getId(), System.currentTimeMillis());

        return true;
    }

    private boolean rotateToCrystal(EndCrystalEntity crystal) {
        Vec3d crystalPos = crystal.getPos();
        Vec3d playerPos = mc.player.getEyePos();
        Vec3d direction = crystalPos.subtract(playerPos).normalize();

        float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
        float pitch = (float) -Math.toDegrees(Math.asin(direction.y));

        // Check if rotation is within speed limits
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float yawDiff = Math.abs(yaw - currentYaw);
        float pitchDiff = Math.abs(pitch - currentPitch);

        // Normalize yaw difference
        if (yawDiff > 180) yawDiff = 360 - yawDiff;

        float maxRotation = rotationSpeed.get().floatValue();

        // Apply rotation speed limits
        if (yawDiff > maxRotation) {
            yaw = currentYaw + Math.signum(yaw - currentYaw) * maxRotation;
        }
        if (pitchDiff > maxRotation) {
            pitch = currentPitch + Math.signum(pitch - currentPitch) * maxRotation;
        }

        Rotations.rotate(yaw, pitch);
        return true;
    }

    // === PLACEMENT PATTERN METHODS ===
    private List<BlockPos> arrangeSurrounding(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Sort positions by distance from target
        positions.sort((a, b) -> Double.compare(
            a.getSquaredDistance(target),
            b.getSquaredDistance(target)
        ));

        // Select positions in a surrounding pattern
        BlockPos[] offsets = {
            target.add(1, 0, 0), target.add(-1, 0, 0),
            target.add(0, 0, 1), target.add(0, 0, -1),
            target.add(1, 0, 1), target.add(-1, 0, -1),
            target.add(1, 0, -1), target.add(-1, 0, 1)
        };

        for (BlockPos offset : offsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeLinear(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = Vec3d.ofCenter(target);
        Vec3d direction = targetPos.subtract(playerPos).normalize();

        // Find positions along the line from player to target
        positions.sort((a, b) -> {
            Vec3d aVec = Vec3d.ofCenter(a);
            Vec3d bVec = Vec3d.ofCenter(b);
            double aDot = aVec.subtract(playerPos).normalize().dotProduct(direction);
            double bDot = bVec.subtract(playerPos).normalize().dotProduct(direction);
            return Double.compare(bDot, aDot);
        });

        for (int i = 0; i < Math.min(count, positions.size()); i++) {
            arranged.add(positions.get(i));
        }

        return arranged;
    }

    private List<BlockPos> arrangeCross(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Cross pattern around target
        BlockPos[] crossOffsets = {
            target.add(2, 0, 0), target.add(-2, 0, 0),
            target.add(0, 0, 2), target.add(0, 0, -2),
            target.add(1, 0, 0) // Center if needed
        };

        for (BlockPos offset : crossOffsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeDiamond(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Diamond pattern
        BlockPos[] diamondOffsets = {
            target.add(2, 0, 0), target.add(-2, 0, 0),
            target.add(0, 0, 2), target.add(0, 0, -2),
            target.add(1, 0, 1), target.add(-1, 0, -1),
            target.add(1, 0, -1), target.add(-1, 0, 1)
        };

        for (BlockPos offset : diamondOffsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeTrap(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Trap formation - prioritize positions that block escape routes
        Vec3d targetVel = currentTarget.getVelocity();
        BlockPos escapeDirection = target.add(
            (int) Math.signum(targetVel.x),
            0,
            (int) Math.signum(targetVel.z)
        );

        // Block escape route first
        BlockPos closest = findClosestPosition(positions, escapeDirection);
        if (closest != null) {
            arranged.add(closest);
        }

        // Then surround
        BlockPos[] trapOffsets = {
            target.add(1, 0, 0), target.add(-1, 0, 0),
            target.add(0, 0, 1), target.add(0, 0, -1)
        };

        for (BlockPos offset : trapOffsets) {
            if (arranged.size() >= count) break;
            BlockPos pos = findClosestPosition(positions, offset);
            if (pos != null && !arranged.contains(pos)) {
                arranged.add(pos);
            }
        }

        return arranged;
    }

    // === AutoGap Helper Methods ===

    private boolean shouldGapEat() {
        if (mc.player == null) return false;
        float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        return health <= gapHealthThreshold.get();
    }

    private boolean isGapItem(ItemStack stack) {
        if (stack == null || stack.isEmpty()) return false;
        Item item = stack.getItem();
        return item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE;
    }

    private int findGapSlot() {
        // Prefer enchanted golden apple if available
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.ENCHANTED_GOLDEN_APPLE) return i;
        }
        // Otherwise, normal golden apple
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.GOLDEN_APPLE) return i;
        }
        return -1;
    }

    private void stopGapEating() {
        changeGapSlot(gapPrevSlot);
        mc.options.useKey.setPressed(false);
        gapEating = false;
    }

    private void changeGapSlot(int slot) {
        if (slot < 0 || slot > 8) return;
        InvUtils.swap(slot, false);
    }

    private void gapEat() {
        changeGapSlot(gapSlot);
        mc.options.useKey.setPressed(true);
        gapEating = true;
    }

    private void startGapEating() {
        gapPrevSlot = mc.player.getInventory().selectedSlot;
        gapEat();
    }

    private BlockPos findClosestPosition(List<BlockPos> positions, BlockPos target) {
        BlockPos closest = null;
        double minDistance = Double.MAX_VALUE;

        for (BlockPos pos : positions) {
            double distance = pos.getSquaredDistance(target);
            if (distance < minDistance) {
                minDistance = distance;
                closest = pos;
            }
        }

        return closest;
    }

    // === STEP FUNCTIONALITY ===
    private void updateStepLogic() {
        if (!enableStep.get() || mc.player == null) return;

        // Check if player is in hole
        boolean currentlyInHole = isInHole(mc.player.getBlockPos());

        // Disable step if entering hole and hole disable is enabled
        if (stepHoleDisable.get() && currentlyInHole && !alreadyInHole) {
            setStepHeight(0.6f);
            alreadyInHole = currentlyInHole;
            return;
        }
        alreadyInHole = currentlyInHole;

        // Pause if shift is pressed
        if (stepPauseIfShift.get() && mc.options.sneakKey.isPressed()) {
            setStepHeight(0.6f);
            return;
        }

        // Don't step if flying, riding, or in water
        if (mc.player.getAbilities().flying || mc.player.isRiding() || mc.player.isTouchingWater()) {
            setStepHeight(0.6f);
            return;
        }

        // Fast fall logic
        if (fastFall.get() && !mc.player.isOnGround() && isOverHole()) {
            Vec3d velocity = mc.player.getVelocity();
            if (velocity.y < 0) {
                mc.player.setVelocity(velocity.x, velocity.y * fastFallSpeed.get(), velocity.z);
            }
        }

        // Reset timer when on ground
        if (stepTimer && mc.player.isOnGround()) {
            stepTimer = false;
        }

        // Set step height based on delay - Thunder Hack style
        if (mc.player.isOnGround() && stepDelayTimer.hasTimePassed(stepDelay.get())) {
            setStepHeight((float) stepHeight.get().doubleValue());
        } else {
            setStepHeight(0.6f);
        }
    }

    private void updateFastUse() {
        if (!enableFastUse.get() || mc.player == null) return;

        ItemStack mainHandStack = mc.player.getMainHandStack();
        if (mainHandStack.isEmpty()) return;

        // Check if item should have fast use applied
        if (!shouldFastUse(mainHandStack.getItem())) return;

        // Speed limiting
        long currentTime = System.currentTimeMillis();
        long currentSecond = currentTime / 1000;

        if (currentSecond != fastUseSecond) {
            fastUseSecond = currentSecond;
            fastUseCount = 0;
        }

        if (fastUseCount >= fastUseSpeedLimit.get()) return;

        // Apply fast use through reflection or mixin access
        try {
            // This would need proper mixin implementation
            // For now, we'll use a simple cooldown reduction approach
            if ((currentTime - lastFastUseTime) >= (fastUseDelay.get() * 50)) { // Convert ticks to ms
                lastFastUseTime = currentTime;
                fastUseCount++;
            }
        } catch (Exception e) {
            // Silently handle any reflection errors
        }
    }

    private boolean shouldFastUse(Item item) {
        if (fastUseAll.get()) return true;

        if (fastUseBlocks.get() && item instanceof BlockItem) return true;
        if (fastUseCrystals.get() && item == Items.END_CRYSTAL) return true;
        if (fastUseXP.get() && item == Items.EXPERIENCE_BOTTLE) return true;

        return false;
    }

    private void setStepHeight(float height) {
        if (mc.player == null) return;

        try {
            // Set step height using entity attributes - Thunder Hack style
            mc.player.getAttributeInstance(EntityAttributes.GENERIC_STEP_HEIGHT).setBaseValue(height);

            if (debugMode.get() && height != 0.6f) {
                info("Step height set to: " + height);
            }
        } catch (Exception e) {
            if (debugMode.get()) {
                error("Failed to set step height: " + e.getMessage());
            }
        }
    }

    private boolean isInHole(BlockPos pos) {
        // Simple hole detection - check if surrounded by solid blocks
        BlockPos[] surroundingPositions = {
            pos.add(1, 0, 0), pos.add(-1, 0, 0),
            pos.add(0, 0, 1), pos.add(0, 0, -1)
        };

        for (BlockPos checkPos : surroundingPositions) {
            if (!mc.world.getBlockState(checkPos).isSolidBlock(mc.world, checkPos)) {
                return false;
            }
        }
        return true;
    }

    private boolean isOverHole() {
        if (mc.player == null) return false;

        // Check if there's a hole below the player
        BlockPos belowPos = mc.player.getBlockPos().down();
        return isInHole(belowPos);
    }

    // Packet handling for step mode and inhibit tracking
    @EventHandler
    public void onPacketSend(PacketEvent.Send event) {
        if (mc.player == null) return;

        // Handle step packets for NCP mode - Thunder Hack style
        if (enableStep.get() && stepMode.get() == StepMode.NCP) {
            handleStepPackets(event);
        }
    }

    /**
     * Handle step packets using Thunder Hack implementation
     */
    private void handleStepPackets(PacketEvent.Send event) {
        if (!(event.packet instanceof PlayerMoveC2SPacket)) return;

        double stepHeight = mc.player.getY() - mc.player.prevY;

        if (stepHeight <= 0.75 || stepHeight > this.stepHeight.get() ||
            (stepStrict.get() && stepHeight > 1)) return;

        double[] offsets = getStepOffsets(stepHeight);
        if (offsets != null && offsets.length > 1) {
            if (stepUseTimer.get()) {
                // Timer manipulation for smoother stepping
                stepTimer = true;
            }

            // Send step packets - Thunder Hack style
            for (double offset : offsets) {
                mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(
                    mc.player.prevX, mc.player.prevY + offset, mc.player.prevZ, false, false));
            }

            if (stepStrict.get()) {
                mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(
                    mc.player.prevX, mc.player.prevY + stepHeight, mc.player.prevZ, false, false));
            }

            stepDelayTimer.reset();
        }
    }

    // Track opponent crystal placement attempts for inhibit mode
    @EventHandler
    public void onPacketReceive(PacketEvent.Receive event) {
        if (!inhibit.get() || !inhibitPlacement.get()) return;
        if (mc.player == null || mc.world == null) return;

        // Track when crystals are spawned by other players
        if (event.packet instanceof net.minecraft.network.packet.s2c.play.EntitySpawnS2CPacket packet) {
            if (packet.getEntityType() == EntityType.END_CRYSTAL) {
                Vec3d spawnPos = new Vec3d(packet.getX(), packet.getY(), packet.getZ());
                BlockPos crystalPos = BlockPos.ofFloored(spawnPos);

                // Check if this crystal was placed near us and we should inhibit the area
                if (mc.player.getBlockPos().isWithinDistance(crystalPos, inhibitRange.get())) {
                    // Track this as an opponent placement attempt
                    opponentPlacementAttempts.put(crystalPos, System.currentTimeMillis());

                    // Queue this position for obsidian placement after delay (to block future placements)
                    inhibitPlacementQueue.put(crystalPos, inhibitDelay.get());

                    if (debugMode.get()) {
                        info("§cTracked opponent crystal spawn at " + crystalPos.toShortString() + " - queuing inhibit");
                    }
                }
            }
        }
    }



    /**
     * Get step offsets for NCP bypass - Thunder Hack implementation
     */
    private double[] getStepOffsets(double height) {
        return switch ((int) (height * 10000)) {
            case 7500, 10000 -> new double[]{0.42, 0.753};
            case 8125, 8750 -> new double[]{0.39, 0.7};
            case 15000 -> new double[]{0.42, 0.75, 1.0, 1.16, 1.23, 1.2};
            case 20000 -> new double[]{0.42, 0.78, 0.63, 0.51, 0.9, 1.21, 1.45, 1.43};
            case 250000 -> new double[]{0.425, 0.821, 0.699, 0.599, 1.022, 1.372, 1.652, 1.869, 2.019, 1.907};
            default -> null;
        };
    }

    // === FOOT TARGETING SYSTEM ===
    private void updateFootTargeting() {
        if (mc.player == null || mc.world == null) return;

        // Clear old data
        footTargetPositions.clear();

        // Find all valid targets
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof PlayerEntity player)) continue;
            if (player == mc.player) continue;
            if (!isValidTarget(player)) continue;

            double distance = mc.player.distanceTo(player);
            if (distance > footTargetRange.get()) continue;

            // Calculate foot positions around the target
            List<BlockPos> footPositions = calculateFootPositions(player);
            if (!footPositions.isEmpty()) {
                footTargetPositions.put(player, footPositions);

                // Mine obstructing obsidian if enabled
                if (mineObsidian.get()) {
                    mineObstructingObsidian(player, footPositions);
                }
            }
        }
    }

    private List<BlockPos> calculateFootPositions(PlayerEntity target) {
        List<BlockPos> positions = new ArrayList<>();
        BlockPos targetPos = target.getBlockPos();

        // Check positions around the target's feet
        BlockPos[] offsets = {
            targetPos.add(1, 0, 0), targetPos.add(-1, 0, 0),
            targetPos.add(0, 0, 1), targetPos.add(0, 0, -1),
            targetPos.add(1, 0, 1), targetPos.add(-1, 0, -1),
            targetPos.add(1, 0, -1), targetPos.add(-1, 0, 1)
        };

        for (BlockPos pos : offsets) {
            if (isValidFootPosition(pos, target)) {
                positions.add(pos);
            }
        }

        // Sort by damage potential
        positions.sort((a, b) -> {
            double damageA = calculateCrystalDamage(a.up(), target);
            double damageB = calculateCrystalDamage(b.up(), target);
            return Double.compare(damageB, damageA);
        });

        return positions;
    }

    private boolean isValidFootPosition(BlockPos pos, PlayerEntity target) {
        if (mc.world == null) return false;

        // Check if position is within range
        if (mc.player.getBlockPos().getSquaredDistance(pos) > Math.pow(footTargetRange.get(), 2)) {
            return false;
        }

        // Check if the base block is valid for crystal placement
        Block baseBlock = mc.world.getBlockState(pos).getBlock();
        if (!validCrystalBaseBlocks.get().contains(baseBlock)) {
            return false;
        }

        // Check if crystal position is clear
        BlockPos crystalPos = pos.up();
        if (!mc.world.getBlockState(crystalPos).isAir()) {
            return false;
        }
        if (!mc.world.getBlockState(crystalPos.up()).isAir()) {
            return false;
        }

        // Check if crystal would deal minimum damage
        double damage = calculateCrystalDamage(crystalPos, target);
        return damage >= minDamagePlace.get();
    }

    private void mineObstructingObsidian(PlayerEntity target, List<BlockPos> footPositions) {
        if (mc.player == null || mc.world == null) return;

        int minedCount = 0;
        BlockPos targetPos = target.getBlockPos();

        // Check blocks around target's feet for obsidian
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                if (minedCount >= maxObsidianMine.get()) break;

                BlockPos checkPos = targetPos.add(x, 0, z);
                double distance = mc.player.getBlockPos().getSquaredDistance(checkPos);

                if (distance > Math.pow(obsidianMineRange.get(), 2)) continue;
                if (currentlyMining.contains(checkPos)) continue;

                Block block = mc.world.getBlockState(checkPos).getBlock();
                if (block != Blocks.OBSIDIAN) continue;

                // Check if mining this block would create a valid crystal position
                if (wouldCreateCrystalPosition(checkPos, target)) {
                    startMiningObsidian(checkPos);
                    minedCount++;
                }
            }
        }
    }

    private boolean wouldCreateCrystalPosition(BlockPos obsidianPos, PlayerEntity target) {
        // Check if removing this obsidian would allow crystal placement
        BlockPos crystalPos = obsidianPos.up();

        // Ensure crystal position would be clear
        if (!mc.world.getBlockState(crystalPos).isAir()) return false;
        if (!mc.world.getBlockState(crystalPos.up()).isAir()) return false;

        // Check if there's a valid base block nearby
        BlockPos[] basePositions = {
            obsidianPos.add(1, 0, 0), obsidianPos.add(-1, 0, 0),
            obsidianPos.add(0, 0, 1), obsidianPos.add(0, 0, -1)
        };

        for (BlockPos basePos : basePositions) {
            Block baseBlock = mc.world.getBlockState(basePos).getBlock();
            if (validCrystalBaseBlocks.get().contains(baseBlock)) {
                double damage = calculateCrystalDamage(basePos.up(), target);
                if (damage >= minDamagePlace.get()) {
                    return true;
                }
            }
        }

        return false;
    }

    private void startMiningObsidian(BlockPos pos) {
        if (mc.player == null || mc.interactionManager == null) return;

        // Find pickaxe (use city mining pickaxe types for consistency)
        List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
        FindItemResult pickaxe = InvUtils.find(itemStack -> pickaxeTypes.contains(itemStack.getItem()));
        if (!pickaxe.isHotbar()) return;

        // Switch to pickaxe immediately for proper server validation
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(pickaxe.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Rotate to target if needed
        if (rotationMode.get() == RotationMode.Both || rotationMode.get() == RotationMode.Break) {
            Rotations.rotate(Rotations.getYaw(pos), Rotations.getPitch(pos));
        }

        // Start mining with proper packet sequence
        Direction direction = BlockUtils.getDirection(pos);

        if (cityMiningPacketMine.get()) {
            // Use packet mining for better server validation
            mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
                PlayerActionC2SPacket.Action.START_DESTROY_BLOCK, pos, direction));
        } else {
            // Use interaction manager
            mc.interactionManager.updateBlockBreakingProgress(pos, direction);
        }

        // Track mining state
        currentlyMining.add(pos);
        obsidianMiningProgress.put(pos, System.currentTimeMillis());

        // Swing hand for visual feedback
        if (cityMiningSwingHand.get()) {
            mc.player.swingHand(Hand.MAIN_HAND);
        }

        if (debugMode.get()) {
            String pickaxeName = mc.player.getInventory().getStack(pickaxe.slot()).getItem().getName().getString();
            info("Started mining obsidian at " + pos.toShortString() + " for foot targeting with pickaxe: " + pickaxeName);
        }
    }



    private boolean isValidTarget(PlayerEntity player) {
        if (player == mc.player) return false;
        if (player.isDead() || player.getHealth() <= 0) return false;
        if (Friends.get().isFriend(player)) return false;

        // Check if player has armor (if ignoreNaked is enabled)
        if (ignoreNaked.get()) {
            boolean hasArmor = false;
            for (ItemStack armorPiece : player.getArmorItems()) {
                if (!armorPiece.isEmpty()) {
                    hasArmor = true;
                    break;
                }
            }
            if (!hasArmor) return false;
        }

        return true;
    }

    // === HOLE ANCHOR SYSTEM ===
    private void updateHoleAnchor() {
        if (mc.player == null || mc.world == null) return;

        // Check if player is looking down enough to activate anchoring
        if (mc.player.getPitch() > anchorPitch.get()) {
            BlockPos playerPos = mc.player.getBlockPos();

            // Check for valid holes below player
            boolean foundHole = false;
            for (int i = 1; i <= anchorDepthCheck.get(); i++) {
                BlockPos checkPos = playerPos.down(i);
                if (isValidIndestructibleHole(checkPos) || isValidTwoBlockIndestructibleHole(checkPos)) {
                    foundHole = true;
                    anchoredHole = checkPos;
                    break;
                }
            }

            if (foundHole) {
                isAnchored = true;

                if (!anchorPull.get()) {
                    // Just stop horizontal movement
                    mc.player.setVelocity(0, mc.player.getVelocity().getY(), 0);
                } else {
                    // Pull player to hole center
                    Vec3d center = new Vec3d(
                        Math.floor(mc.player.getX()) + 0.5,
                        Math.floor(mc.player.getY()),
                        Math.floor(mc.player.getZ()) + 0.5
                    );

                    if (Math.abs(center.x - mc.player.getX()) > 0.1 || Math.abs(center.z - mc.player.getZ()) > 0.1) {
                        double deltaX = center.x - mc.player.getX();
                        double deltaZ = center.z - mc.player.getZ();
                        double pullStrength = anchorPullStrength.get();

                        mc.player.setVelocity(
                            Math.min(deltaX / 2.0, pullStrength),
                            mc.player.getVelocity().getY(),
                            Math.min(deltaZ / 2.0, pullStrength)
                        );
                    }
                }
            } else {
                isAnchored = false;
                anchoredHole = null;
            }
        } else {
            isAnchored = false;
            anchoredHole = null;
        }
    }

    // === HOLE SNAP SYSTEM ===
    private void updateHoleSnap() {
        if (mc.player == null || mc.world == null) return;

        // Auto-disable checks
        if (holeAutoDisableOnDeath.get() && (mc.player.getHealth() + mc.player.getAbsorptionAmount() <= 0 || mc.player.isDead())) {
            info("Player died! Disabling hole snap...");
            // Note: In a real implementation, you'd disable the module here
            return;
        }

        if (holeAutoDisableInHole.get() && mc.player.getBlockPos().equals(targetHole)) {
            info("Already in target hole! Disabling hole snap...");
            // Note: In a real implementation, you'd disable the module here
            return;
        }

        // Search for holes periodically
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastHoleSearch > 500) { // Search every 500ms
            targetHole = findNearestHole();
            lastHoleSearch = currentTime;

            if (targetHole == null && holeAutoDisableNoHoles.get()) {
                info("No holes found! Disabling hole snap...");
                // Note: In a real implementation, you'd disable the module here
                return;
            }
        }

        if (targetHole == null) return;

        // Handle movement based on mode
        if (holeSnapMode.get() == HoleSnapMode.Move) {
            handleMoveMode();
        } else if (holeSnapMode.get() == HoleSnapMode.Yaw) {
            handleYawMode();
        }

        // Handle collisions
        if (mc.player.horizontalCollision && mc.player.isOnGround()) {
            mc.player.jump();
        }
    }

    private void handleMoveMode() {
        if (targetHole == null || mc.player == null) return;

        Vec3d holeCenter = targetHole.toCenterPos();
        double newYaw = Math.cos(Math.toRadians(getNewYaw(holeCenter) + 90.0f));
        double newPitch = Math.sin(Math.toRadians(getNewYaw(holeCenter) + 90.0f));
        double diffX = holeCenter.getX() - mc.player.getX();
        double diffZ = holeCenter.getZ() - mc.player.getZ();
        double x = 0.29 * newYaw;
        double z = 0.29 * newPitch;

        // Apply movement
        Vec3d currentVelocity = mc.player.getVelocity();
        mc.player.setVelocity(
            Math.abs(x) < Math.abs(diffX) ? x : diffX,
            currentVelocity.getY(),
            Math.abs(z) < Math.abs(diffZ) ? z : diffZ
        );
    }

    private void handleYawMode() {
        if (targetHole == null || mc.player == null) return;

        // Store previous yaw and set new yaw towards hole
        prevClientYaw = mc.player.getYaw();
        float targetYaw = calculateAngleToHole(targetHole.toCenterPos())[0];
        mc.player.setYaw(targetYaw);

        // Force forward movement
        if (mc.player.input != null) {
            mc.player.input.movementForward = 1;
        }
    }

    private BlockPos findNearestHole() {
        if (mc.player == null || mc.world == null) return null;

        List<BlockPos> holes = new ArrayList<>();
        BlockPos centerPos = mc.player.getBlockPos();

        // Search for holes in range
        for (int x = centerPos.getX() - holeSearchRange.get(); x < centerPos.getX() + holeSearchRange.get(); x++) {
            for (int y = centerPos.getY() - 4; y < centerPos.getY() + 2; y++) {
                for (int z = centerPos.getZ() - holeSearchRange.get(); z < centerPos.getZ() + holeSearchRange.get(); z++) {
                    BlockPos pos = new BlockPos(x, y, z);

                    if (isValidIndestructibleHole(pos) && isVecInFOV(pos.toCenterPos(), holeSearchFOV.get() / 2)) {
                        holes.add(pos);
                    }
                }
            }
        }

        // Find nearest hole
        BlockPos nearestHole = null;
        double nearestDistance = Double.MAX_VALUE;

        for (BlockPos hole : holes) {
            if (mc.player.getBlockPos().equals(hole) && holeAutoDisableInHole.get()) {
                info("Already in a hole! Disabling hole snap...");
                return null;
            }

            double distance = mc.player.squaredDistanceTo(hole.toCenterPos());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestHole = hole;
            }
        }

        return nearestHole;
    }

    private float getNewYaw(Vec3d pos) {
        if (mc.player == null) return 0;

        return mc.player.getYaw() + MathHelper.wrapDegrees(
            (float) Math.toDegrees(Math.atan2(pos.getZ() - mc.player.getZ(), pos.getX() - mc.player.getX()))
            - mc.player.getYaw() - 90
        );
    }

    private float[] calculateAngleToHole(Vec3d pos) {
        if (mc.player == null) return new float[]{0, 0};

        double deltaX = pos.getX() - mc.player.getX();
        double deltaY = pos.getY() - mc.player.getY();
        double deltaZ = pos.getZ() - mc.player.getZ();

        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        float yaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0f;
        float pitch = (float) -Math.toDegrees(Math.atan2(deltaY, distance));

        return new float[]{yaw, pitch};
    }

    private boolean isVecInFOV(Vec3d vec, int fov) {
        if (mc.player == null) return false;

        float angle = Math.abs(getNewYaw(vec) - mc.player.getYaw());
        return angle <= fov;
    }

    // Simplified hole validation methods (would need proper implementation)
    private boolean isValidIndestructibleHole(BlockPos pos) {
        if (mc.world == null) return false;

        // Check if position has solid indestructible blocks around it
        BlockPos[] surroundingPositions = {
            pos.add(1, 0, 0), pos.add(-1, 0, 0),
            pos.add(0, 0, 1), pos.add(0, 0, -1)
        };

        for (BlockPos checkPos : surroundingPositions) {
            Block block = mc.world.getBlockState(checkPos).getBlock();
            if (block != Blocks.OBSIDIAN && block != Blocks.BEDROCK) {
                return false;
            }
        }

        // Check if the hole itself is air
        return mc.world.getBlockState(pos).isAir() && mc.world.getBlockState(pos.up()).isAir();
    }

    private boolean isValidTwoBlockIndestructibleHole(BlockPos pos) {
        // Check for 2-block high holes
        return isValidIndestructibleHole(pos) && mc.world != null && mc.world.getBlockState(pos.up(2)).isAir();
    }

    // === STRAFE SYSTEM ===
    private void updateStrafe() {
        if (mc.player == null || mc.world == null) return;

        // Update old speed based on actual movement
        oldSpeed = Math.hypot(
            mc.player.getX() - mc.player.prevX,
            mc.player.getZ() - mc.player.prevZ
        ) * contextFriction;

        // Handle elytra boost
        if (strafeBoost.get() == StrafeBoost.Elytra) {
            handleElytraBoost();
        }
    }

    private void handleElytraBoost() {
        if (mc.player == null || mc.world == null) return;

        // Find elytra in inventory
        int elytraSlot = findElytraSlot();
        if (elytraSlot == -1) return;

        // Check conditions for elytra boost
        if (isMoving() && !mc.player.isOnGround() && mc.player.fallDistance > 0 && !strafeDisabled) {
            boolean shouldActivate = !mc.world.getBlockCollisions(
                mc.player,
                mc.player.getBoundingBox().offset(0.0, -1.1f, 0.0f)
            ).iterator().hasNext() || !strafeSunrise.get();

            if (shouldActivate) {
                activateElytraBoost(elytraSlot);
            }
        }

        // Handle collision speed boost
        if (isMoving() && !mc.player.isOnGround() &&
            mc.world.getBlockCollisions(mc.player, mc.player.getBoundingBox().offset(0.0, 0.0, 0.0f)).iterator().hasNext() &&
            strafeDisabled) {
            oldSpeed = strafeSpeed.get();
        }
    }

    private void activateElytraBoost(int elytraSlot) {
        if (System.currentTimeMillis() - strafeDisableTime <= 190L) return;

        try {
            // Swap elytra to chestplate slot
            if (elytraSlot != -2) {
                mc.interactionManager.clickSlot(0, elytraSlot, 1, SlotActionType.PICKUP, mc.player);
                mc.interactionManager.clickSlot(0, 6, 1, SlotActionType.PICKUP, mc.player);
            }

            // Send elytra activation packets
            mc.player.networkHandler.sendPacket(new ClientCommandC2SPacket(mc.player, ClientCommandC2SPacket.Mode.START_FALL_FLYING));
            mc.player.networkHandler.sendPacket(new ClientCommandC2SPacket(mc.player, ClientCommandC2SPacket.Mode.START_FALL_FLYING));

            // Swap back
            if (elytraSlot != -2) {
                mc.interactionManager.clickSlot(0, 6, 1, SlotActionType.PICKUP, mc.player);
                mc.interactionManager.clickSlot(0, elytraSlot, 1, SlotActionType.PICKUP, mc.player);
            }

            strafeDisableTime = System.currentTimeMillis();
            strafeDisabled = true;

            if (debugMode.get()) {
                info("Activated elytra boost");
            }
        } catch (Exception e) {
            if (debugMode.get()) {
                error("Failed to activate elytra boost: " + e.getMessage());
            }
        }
    }

    private double calculateStrafeSpeed() {
        if (mc.player == null || mc.world == null) return 0;

        float speedAttributes = getAIMoveSpeed();
        BlockPos playerPos = BlockPos.ofFloored(mc.player.getPos());
        final float frictionFactor = mc.world.getBlockState(playerPos.down()).getBlock().getSlipperiness() * 0.91F;

        float friction = mc.player.hasStatusEffect(StatusEffects.JUMP_BOOST) && mc.player.isUsingItem() ? 0.88f :
                        (oldSpeed > 0.32 && mc.player.isUsingItem() ? 0.88f : 0.91F);

        if (mc.player.isOnGround()) {
            friction = frictionFactor;
        }

        float speedFactor = (float) (0.1631f / Math.pow(friction, 3.0f));
        float speedIncrease;

        if (mc.player.isOnGround()) {
            speedIncrease = speedAttributes * speedFactor;
            if (mc.player.getVelocity().getY() > 0) {
                speedIncrease += (strafeBoost.get() == StrafeBoost.Elytra && findElytraSlot() != -1 &&
                                 (strafeDisabled && System.currentTimeMillis() - strafeDisableTime < 300)) ? 0.65f : 0.2f;
            }
            strafeDisabled = false;
        } else {
            speedIncrease = 0.0255f;
        }

        boolean noSlow = false;
        double maxSpeed = oldSpeed + speedIncrease;
        double slowedSpeed = 0.0;

        if (mc.player.isUsingItem() && mc.player.getVelocity().getY() <= 0 && !strafeSunrise.get()) {
            double slowSpeed = oldSpeed + speedIncrease * 0.25;
            double motionY = mc.player.getVelocity().getY();
            if (motionY != 0.0 && Math.abs(motionY) < 0.08) {
                slowSpeed += 0.055;
            }
            if (maxSpeed > (slowedSpeed = Math.max(0.043, slowSpeed))) {
                noSlow = true;
                ++noSlowTicks;
            } else {
                noSlowTicks = Math.max(noSlowTicks - 1, 0);
            }
        } else {
            noSlowTicks = 0;
        }

        if (noSlowTicks > 3) {
            maxSpeed = slowedSpeed - 0.019;
        } else {
            maxSpeed = Math.max(noSlow ? 0 : 0.25, maxSpeed) - (mc.player.age % 2 == 0 ? 0.001 : 0.002);
        }

        contextFriction = friction;
        if (!mc.player.isOnGround()) {
            needSprintState = !mc.player.isSprinting();
            needSwap = true;
        } else {
            needSprintState = false;
        }

        return maxSpeed;
    }

    private float getAIMoveSpeed() {
        if (mc.player == null) return 0;

        boolean prevSprinting = mc.player.isSprinting();
        mc.player.setSprinting(false);
        float speed = mc.player.getMovementSpeed() * 1.3f;
        mc.player.setSprinting(prevSprinting);
        return speed;
    }

    private boolean canStrafe() {
        if (mc.player == null) return false;
        if (mc.player.isSneaking()) return false;
        if (mc.player.isInLava()) return false;
        if (mc.player.isSubmergedInWater()) return false;
        if (mc.player.getAbilities().flying) return false;

        // Don't strafe if other movement modules are active
        // This would need proper module manager integration
        return true;
    }

    private boolean isMoving() {
        if (mc.player == null) return false;
        return mc.player.input.movementForward != 0 || mc.player.input.movementSideways != 0;
    }

    private int findElytraSlot() {
        if (mc.player == null) return -1;

        // Check if elytra is already equipped
        ItemStack chestplate = mc.player.getInventory().getArmorStack(2);
        if (chestplate.getItem() == Items.ELYTRA) {
            return -2; // Already equipped
        }

        // Search inventory for elytra
        for (int i = 0; i < mc.player.getInventory().size(); i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.ELYTRA) {
                return i;
            }
        }

        return -1; // Not found
    }

    // === KEYBIND HANDLERS ===
    private void handleKeybinds() {
        // Hyper-aggressive mode toggle
        if (hyperAggressiveKeybind.get().isPressed()) {
            hyperAggressiveMode.set(!hyperAggressiveMode.get());
            info("Hyper-aggressive mode: " + (hyperAggressiveMode.get() ? "§cENABLED" : "§aDisabled"));
        }

        // Face-place toggle
        if (facePlaceKeybind.get().isPressed()) {
            facePlaceMode.set(!facePlaceMode.get());
            info("Face-place mode: " + (facePlaceMode.get() ? "§cENABLED" : "§aDisabled"));
        }

        // Auto-pearl trigger/toggle
        if (autoPearlKeybind.get().isPressed()) {
            if (autoPearlMode.get() != AutoPearlMode.Disabled) {
                // Manual trigger
                triggerEmergencyEscape();
                info("§eManual emergency escape triggered!");
            } else {
                // Toggle auto-pearl
                autoPearlMode.set(AutoPearlMode.Combined);
                info("Auto-pearl: §aENABLED");
            }
        }

        // Inhibit mode toggle
        if (inhibitKeybind.get().isPressed()) {
            inhibit.set(!inhibit.get());
            info("Inhibit mode: " + (inhibit.get() ? "§cENABLED" : "§aDisabled"));
        }

        // Strafe toggle
        if (strafeToggleKeybind.get().isPressed()) {
            enableStrafe.set(!enableStrafe.get());
            info("Strafe: " + (enableStrafe.get() ? "§aENABLED" : "§cDisabled"));

            if (!enableStrafe.get() && mc.options != null && strafeDisableFOV.get()) {
                mc.options.getFovEffectScale().setValue(originalFovValue);
            }
        }

        // Strafe boost mode cycling
        if (strafeBoostKeybind.get().isPressed()) {
            StrafeBoost currentBoost = strafeBoost.get();
            StrafeBoost nextBoost = switch (currentBoost) {
                case None -> StrafeBoost.Elytra;
                case Elytra -> StrafeBoost.Damage;
                case Damage -> StrafeBoost.None;
            };
            strafeBoost.set(nextBoost);
            info("Strafe boost mode: §e" + nextBoost.toString());
        }

        // Hole fill toggle
        if (holeFillKeybind.get().isPressed()) {
            enableHoleFill.set(!enableHoleFill.get());
            info("Hole Fill: " + (enableHoleFill.get() ? "§aENABLED" : "§cDisabled"));
        }
    }

    private void triggerEmergencyEscape() {
        // This would trigger the emergency escape logic
        // Implementation would depend on the existing auto-pearl system
        if (debugMode.get()) {
            info("Emergency escape triggered via keybind");
        }
    }

    // === HOLE FILL SYSTEM ===
    private void updateHoleFill() {
        if (mc.player == null || mc.world == null) return;

        // Jump disable check
        if (holeFillJumpDisable.get() && mc.player.prevY < mc.player.getY()) {
            info("Player jumped! Disabling hole fill...");
            enableHoleFill.set(false);
            return;
        }

        // Delay check
        if (holeFillTickCounter < holeFillDelay.get()) {
            holeFillTickCounter++;
            return;
        }

        // Find valid block slot
        int blockSlot = getHoleFillBlockSlot();
        if (blockSlot == -1) {
            if (debugMode.get()) {
                info("No valid blocks for hole filling");
            }
            return;
        }

        // Find holes to fill
        List<BlockPos> holes = findHolesToFill();
        if (holes.isEmpty()) {
            if (holeFillAutoDisable.get()) {
                info("All holes filled! Disabling hole fill...");
                enableHoleFill.set(false);
            }
            return;
        }

        // Find target if in target mode
        PlayerEntity target = null;
        if (holeFillMode.get() == HoleFillMode.Target) {
            target = findNearestEnemyForHoleFill();
            if (target == null) return;
        }

        // Fill holes
        int blocksPlaced = 0;
        for (BlockPos hole : holes) {
            if (blocksPlaced >= holeFillBlocksPerTick.get()) break;

            if (shouldFillHole(hole, target)) {
                List<BlockPos> positions = getHolePositions(hole);
                for (BlockPos pos : positions) {
                    if (blocksPlaced >= holeFillBlocksPerTick.get()) break;

                    if (canPlaceBlockAt(pos)) {
                        if (placeHoleFillBlock(pos, blockSlot)) {
                            blocksPlaced++;
                            fillingHoles.add(pos);
                            holeRenderTime.put(pos, System.currentTimeMillis());
                            holeFillTickCounter = 0;
                            lastHoleFillTime = System.currentTimeMillis();

                            if (debugMode.get()) {
                                info("Filled hole at " + pos.toShortString());
                            }
                        }
                    }
                }
            }
        }

        // Clean up old render positions
        cleanupHoleRender();
    }

    private List<BlockPos> findHolesToFill() {
        List<BlockPos> holes = new ArrayList<>();
        BlockPos centerPos = mc.player.getBlockPos();
        int range = (int) Math.ceil(holeFillRange.get()) + 1;
        int height = holeFillRange.get().intValue();

        for (int x = centerPos.getX() - range; x < centerPos.getX() + range; x++) {
            for (int y = centerPos.getY() - height; y < centerPos.getY() + height; y++) {
                for (int z = centerPos.getZ() - range; z < centerPos.getZ() + range; z++) {
                    BlockPos pos = new BlockPos(x, y, z);

                    if (isValidHoleToFill(pos) && !isCurrentlyFilling(pos)) {
                        // Check if any players are in the hole
                        boolean hasPlayer = false;
                        for (PlayerEntity player : mc.world.getPlayers()) {
                            if (player.getBoundingBox().intersects(new Box(pos))) {
                                hasPlayer = true;
                                break;
                            }
                        }

                        if (!hasPlayer) {
                            // Wall range check
                            double distance = mc.player.getPos().distanceTo(pos.toCenterPos());
                            if (distance <= holeFillRange.get()) {
                                // Line of sight check
                                HitResult raycast = mc.world.raycast(new RaycastContext(
                                    mc.player.getEyePos(),
                                    pos.toCenterPos().add(0, 0.5, 0),
                                    RaycastContext.ShapeType.COLLIDER,
                                    RaycastContext.FluidHandling.NONE,
                                    mc.player
                                ));

                                if (raycast.getType() == HitResult.Type.MISS ||
                                    (raycast instanceof BlockHitResult blockHit && blockHit.getBlockPos().equals(pos)) ||
                                    distance <= holeFillWallRange.get()) {
                                    holes.add(pos);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Sort by distance
        holes.sort(Comparator.comparing(pos -> mc.player.getPos().squaredDistanceTo(pos.toCenterPos())));
        return holes;
    }

    private boolean isValidHoleToFill(BlockPos pos) {
        if (mc.world == null) return false;

        // Check hole types based on settings
        if (holeFillSingle.get() && (isValidIndestructibleHole(pos) || isValidBedrockHole(pos))) {
            return true;
        }

        if (holeFillDouble.get() && (isValidTwoBlockIndestructibleHole(pos) || isValidTwoBlockBedrockHole(pos))) {
            return true;
        }

        if (holeFillQuad.get() && (isValidQuadIndestructibleHole(pos) || isValidQuadBedrockHole(pos))) {
            return true;
        }

        return false;
    }

    private boolean shouldFillHole(BlockPos hole, PlayerEntity target) {
        // Self-fill check
        if (hole.equals(mc.player.getBlockPos()) && holeFillSelf.get()) {
            holeFillSelfNeed = true;
            return true;
        }

        // Target mode check
        if (holeFillMode.get() == HoleFillMode.Target && target != null) {
            double distanceToTarget = target.getPos().distanceTo(hole.toCenterPos());
            return distanceToTarget <= holeFillTargetRange.get();
        }

        return true;
    }

    private List<BlockPos> getHolePositions(BlockPos hole) {
        List<BlockPos> positions = new ArrayList<>();

        // Handle self-fill
        if (holeFillSelfNeed && hole.equals(mc.player.getBlockPos())) {
            if (holeFillSelfMode.get() == SelfFillMode.Trap) {
                // Add position above player's head
                BlockPos headPos = mc.player.getBlockPos().up(2);
                if (mc.world.getBlockState(headPos).isReplaceable()) {
                    positions.add(headPos);
                }

                // Add surrounding positions if head is blocked
                if (positions.isEmpty()) {
                    for (Direction dir : Direction.Type.HORIZONTAL) {
                        for (int i = 0; i < 3; i++) {
                            BlockPos checkPos = headPos.offset(dir).down(i);
                            if (mc.world.getBlockState(checkPos).isReplaceable()) {
                                positions.add(checkPos);
                                break;
                            }
                        }
                    }
                }
            }
            holeFillSelfNeed = false;
            return positions;
        }

        // Regular hole filling
        if (isValidQuadIndestructibleHole(hole) || isValidQuadBedrockHole(hole)) {
            // Quad hole - fill all 4 positions
            Vec3i[] quadVectors = {
                new Vec3i(-1, 0, -1), new Vec3i(1, 0, -1),
                new Vec3i(-1, 0, 1), new Vec3i(1, 0, 1)
            };

            for (Vec3i vec : quadVectors) {
                BlockPos checkPos = hole.add(vec);
                if (mc.world.getBlockState(checkPos).isReplaceable()) {
                    positions.add(hole);
                    positions.add(checkPos);
                    positions.add(hole.add(vec.getX(), 0, 0));
                    positions.add(hole.add(0, 0, vec.getZ()));
                    break;
                }
            }
        } else if (isValidTwoBlockIndestructibleHole(hole) || isValidTwoBlockBedrockHole(hole)) {
            // Double hole - fill both positions
            for (Direction dir : Direction.Type.HORIZONTAL) {
                BlockPos checkPos = hole.offset(dir);
                if (mc.world.getBlockState(hole).isReplaceable() &&
                    mc.world.getBlockState(checkPos).isReplaceable()) {
                    positions.add(hole);
                    positions.add(checkPos);
                    break;
                }
            }
        } else {
            // Single hole
            positions.add(hole);
        }

        return positions;
    }

    private boolean canPlaceBlockAt(BlockPos pos) {
        if (mc.world == null) return false;

        // Check if position is replaceable
        if (!mc.world.getBlockState(pos).isReplaceable()) {
            return false;
        }

        // Check if we can reach the position
        double distance = mc.player.getPos().distanceTo(pos.toCenterPos());
        if (distance > holeFillRange.get()) {
            return false;
        }

        // Check if there's a valid face to place against
        for (Direction dir : Direction.values()) {
            BlockPos adjacentPos = pos.offset(dir);
            if (!mc.world.getBlockState(adjacentPos).isReplaceable()) {
                return true;
            }
        }

        return false;
    }

    private boolean placeHoleFillBlock(BlockPos pos, int slot) {
        if (mc.player == null || mc.interactionManager == null) return false;

        // Switch to block slot
        int previousSlot = mc.player.getInventory().selectedSlot;
        mc.player.getInventory().selectedSlot = slot;

        // Find best face to place against
        Direction bestFace = null;
        for (Direction dir : Direction.values()) {
            BlockPos adjacentPos = pos.offset(dir);
            if (!mc.world.getBlockState(adjacentPos).isReplaceable()) {
                bestFace = dir.getOpposite();
                break;
            }
        }

        if (bestFace == null) {
            mc.player.getInventory().selectedSlot = previousSlot;
            return false;
        }

        // Calculate hit position
        Vec3d hitPos = pos.toCenterPos().add(Vec3d.of(bestFace.getVector()).multiply(0.5));
        BlockHitResult hitResult = new BlockHitResult(hitPos, bestFace, pos.offset(bestFace), false);

        // Place block
        boolean success = mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, hitResult).isAccepted();

        // Restore previous slot
        mc.player.getInventory().selectedSlot = previousSlot;

        return success;
    }

    private int getHoleFillBlockSlot() {
        // Check main hand first
        ItemStack mainHand = mc.player.getMainHandStack();
        if (isValidHoleFillItem(mainHand.getItem())) {
            return mc.player.getInventory().selectedSlot;
        }

        // Check hotbar
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (isValidHoleFillItem(stack.getItem())) {
                return i;
            }
        }

        return -1;
    }

    private boolean isValidHoleFillItem(Item item) {
        if (!(item instanceof BlockItem blockItem)) return false;

        Block block = blockItem.getBlock();
        return switch (holeFillBlocks.get()) {
            case Webs -> block == Blocks.COBWEB;
            case Obsidian -> block == Blocks.OBSIDIAN;
            case Indestructible -> block == Blocks.OBSIDIAN || block == Blocks.CRYING_OBSIDIAN ||
                                  block == Blocks.NETHERITE_BLOCK || block == Blocks.RESPAWN_ANCHOR;
            case All -> true;
        };
    }

    private boolean isCurrentlyFilling(BlockPos pos) {
        return fillingHoles.contains(pos);
    }

    private void cleanupHoleRender() {
        long currentTime = System.currentTimeMillis();
        holeRenderTime.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > 3000) { // 3 second render time
                fillingHoles.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }

    private PlayerEntity findNearestEnemyForHoleFill() {
        if (mc.world == null) return null;

        PlayerEntity nearest = null;
        double nearestDistance = Double.MAX_VALUE;

        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            if (Friends.get().isFriend(player)) continue;

            double distance = mc.player.distanceTo(player);
            if (distance <= holeFillRange.get() && distance < nearestDistance) {
                nearest = player;
                nearestDistance = distance;
            }
        }

        return nearest;
    }

    // Simplified hole validation methods for hole filling
    private boolean isValidBedrockHole(BlockPos pos) {
        if (mc.world == null) return false;

        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;

        // Check surrounding blocks for bedrock
        for (Direction dir : Direction.Type.HORIZONTAL) {
            Block block = mc.world.getBlockState(pos.offset(dir)).getBlock();
            if (block != Blocks.BEDROCK) return false;
        }

        return mc.world.getBlockState(pos.down()).getBlock() == Blocks.BEDROCK;
    }

    private boolean isValidTwoBlockBedrockHole(BlockPos pos) {
        // Check for 2-block bedrock holes
        for (Direction dir : Direction.Type.HORIZONTAL) {
            BlockPos adjacent = pos.offset(dir);
            if (isValidBedrockHole(pos) && isValidBedrockHole(adjacent)) {
                return true;
            }
        }
        return false;
    }

    private boolean isValidQuadBedrockHole(BlockPos pos) {
        // Check for 4-block bedrock holes
        Vec3i[] quadVectors = {
            new Vec3i(-1, 0, -1), new Vec3i(1, 0, -1),
            new Vec3i(-1, 0, 1), new Vec3i(1, 0, 1)
        };

        for (Vec3i vec : quadVectors) {
            BlockPos[] quadPositions = {
                pos, pos.add(vec),
                pos.add(vec.getX(), 0, 0),
                pos.add(0, 0, vec.getZ())
            };

            boolean allValid = true;
            for (BlockPos quadPos : quadPositions) {
                if (!isValidBedrockHole(quadPos)) {
                    allValid = false;
                    break;
                }
            }

            if (allValid) return true;
        }

        return false;
    }

    private boolean isValidQuadIndestructibleHole(BlockPos pos) {
        // Similar to quad bedrock but for obsidian/indestructible blocks
        Vec3i[] quadVectors = {
            new Vec3i(-1, 0, -1), new Vec3i(1, 0, -1),
            new Vec3i(-1, 0, 1), new Vec3i(1, 0, 1)
        };

        for (Vec3i vec : quadVectors) {
            BlockPos[] quadPositions = {
                pos, pos.add(vec),
                pos.add(vec.getX(), 0, 0),
                pos.add(0, 0, vec.getZ())
            };

            boolean allValid = true;
            for (BlockPos quadPos : quadPositions) {
                if (!isValidIndestructibleHole(quadPos)) {
                    allValid = false;
                    break;
                }
            }

            if (allValid) return true;
        }

        return false;
    }

}
